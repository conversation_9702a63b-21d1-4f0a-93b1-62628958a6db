import { defaultZoomFeature, UserDetail } from "@/core/domain/users/user";
import { Role } from "@/types/base";
import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import Cookies from "js-cookie";
import { packages } from "@/core/domain/subscription/subscription";

const cookieStorage = {
  getItem: (name: string) => {
    const value = Cookies.get(name);
    return value ? JSON.parse(value) : null;
  },
  setItem: (name: string, value: string) => {
    Cookies.set(name, JSON.stringify(value), { expires: 7 }); // Set cookie expiry as needed
  },
  removeItem: (name: string) => {
    Cookies.remove(name);
  },
};
interface UserState {
  role?: Role;
  setRole: (role: Role) => void;
  seekers: UserDetail;
  setSeekers: (seekers: UserDetail) => void;
  tempSubscribtionLevel: number;
  setTempSubscribtionLevel: (level: number) => void;
  clearUser: () => void;
  hydrated: boolean;
  setHydrated: (val: boolean) => void;
}
export const baseUser: UserDetail = {
  accounts: {
    about: "",
    citizenship: "",
    credit: {
      amount: 0,
      updatedAt: "",
    },
    facebookSocial: "",
    firstName: "",
    image: "",
    isSubscriber: false,
    language: "",
    lastName: "",
    membership: packages.free,
    twitterSocial: "",
    address: "",
    chat: {
      current: 0,
      max: 0,
    },
    zoomFeature: defaultZoomFeature,
  },
  has2FA: false,
  email: "",
  code: "",
  isActive: false,
  phoneNumber: "",
  phoneCode: "",
  type: "SEEKER",
  setting: {
    messageNotif: false,
    newsletterNotif: false,
    priceAlertNotif: false,
    propertyNotif: false,
    soundNotif: false,
    specialOfferNotif: false,
    surveyNotif: false,
  },
};
export const useUserStore = create<UserState>()(
  persist(
    (set) => ({
      role: undefined,
      setRole: (role) => set({ role }),
      seekers: baseUser,
      setSeekers: (seekers) => set({ seekers }),
      tempSubscribtionLevel: 0,
      setTempSubscribtionLevel: (tempSubscribtionLevel: number) =>
        set({ tempSubscribtionLevel }),
      clearUser: () =>
        set(() => ({
          seekers: baseUser,
        })),
      hydrated: false,
      setHydrated: (val) => set({ hydrated: val }),
    }),
    {
      name: "user",
      storage: createJSONStorage(() => cookieStorage),
      onRehydrateStorage: () => (state) => {
        state?.setHydrated(true);
      },
    }
  )
);
