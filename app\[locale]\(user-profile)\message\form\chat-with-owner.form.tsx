import { useTranslations } from "next-intl"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Form } from "@/components/ui/form"
import TextAreaInput from "@/components/input-form/text-area-input"
import { Button } from "@/components/ui/button"
import { usePostNewChat } from "@/core/applications/mutations/messages/use-post-new-chat"
import { NewChatDto } from "@/core/infrastructures/messages/dto"
import { useToast } from "@/hooks/use-toast"
import { useMessagingStore } from "@/stores/messaging.store"
import { MIN_MESSAGE_COUNT } from "@/lib/constanta/constant"
import { useQueryClient } from "@tanstack/react-query"
import { CHAT_LIST } from "@/core/applications/queries/messages/use-get-chat-list"
import useChatWithOwnerFormSchema from "./chat-with-owner-form.schema"
import { useRouter } from "nextjs-toploader/app"
import { useSearchParams } from "next/navigation"
import { seekersMessageUrl } from "@/lib/constanta/route"

export default function ChatWithOwnerForm({ submitHandler, ownerId, propertyId }: { submitHandler: () => void, ownerId: string, propertyId: string }) {
  const t = useTranslations("seeker")
  const { updateSpecificAllChat } = useMessagingStore(state => state)
  const queryClient = useQueryClient()
  const formSchema = useChatWithOwnerFormSchema()
  const router = useRouter()
  const params = useSearchParams()
  type formSchemaType = z.infer<typeof formSchema>
  const postNewChat = usePostNewChat()
  const form = useForm<formSchemaType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      text: ""
    }
  })
  const { toast } = useToast()
  async function onSubmit(values: formSchemaType) {
    if (values.text.trim().length < MIN_MESSAGE_COUNT) {
      toast({
        title: t('error.messageTooShort.title'),
        description: t('error.messageTooShort.description'),
        variant: "destructive"
      })
      return
    }
    const data: NewChatDto = {
      category: "SEEKER_OWNER",
      requested_by: "CLIENT",
      ref_id: params.get("code") || undefined,
      message: values.text,
      receiver: ownerId
    }
    try {
      await postNewChat.mutateAsync(data)
      queryClient.invalidateQueries({ queryKey: [CHAT_LIST] })
      toast({
        title: t('success.sendMessageToOwner.title'),
        description: t('success.sendMessageToOwner.description')
      })
      submitHandler()
      router.push(seekersMessageUrl)
    } catch (error: any) {
      toast({
        title: t("error.failedSendMessage.title"),
        description: error.response.data.message || "",
        variant: "destructive"
      })
    }
  }
  return <div className="w-full space-y-2">

    <Form {...form} >
      <form onSubmit={form.handleSubmit(onSubmit)} className="z-50">

        <TextAreaInput
          form={form}
          label=""
          name="text"
          placeholder={t('form.placeholder.example.requestHelpToCs')}
        />
      </form>
      <Button loading={postNewChat.isPending} onClick={() => onSubmit(form.getValues())} className="min-w-40 max-sm:w-full" variant={"default-seekers"}>{t('cta.sendRequest')}</Button>
    </Form>
  </div>
}