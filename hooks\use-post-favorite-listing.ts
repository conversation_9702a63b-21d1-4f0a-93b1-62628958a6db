import { usePostFavoriteListing } from "@/core/applications/mutations/listing/use-post-favorite-listing";
import { PostFavoritePropertyDto } from "@/core/infrastructures/listing/dto";
import { ACCESS_TOKEN } from "@/lib/constanta/constant";
import { useUserStore } from "@/stores/user.store";
import Cookies from "js-cookie";
import { useEffect, useState } from "react";
import { PackagesType } from "@/core/domain/subscription/subscription";

export const AUTH_ERROR_CODE = "401";
export function useFavoriteListing(code: string, isFavorite: boolean = false) {
  const { seekers } = useUserStore((state) => state);
  const [favorite, setFavorite] = useState(isFavorite);
  const bearer = Cookies.get(ACCESS_TOKEN);
  const role = useUserStore((state) => state.role);
  const [authenticated, setAuthenticated] = useState(false);
  const [membership, setMembership] = useState<PackagesType>(
    seekers.accounts.membership
  );
  const favoriteListingMutation = usePostFavoriteListing();
  useEffect(() => {
    setMembership(seekers.accounts.membership);
  }, [seekers.accounts.membership]);
  useEffect(() => {
    if (bearer && role == "SEEKER") {
      setAuthenticated(true);
    } else {
      setAuthenticated(false);
    }
  }, [bearer, role]);

  const handleFavorite = async (errorCallback?: (error: any) => void) => {
    if (!bearer && role !== "SEEKER") {
      errorCallback?.(AUTH_ERROR_CODE);
      return;
    }

    const data: PostFavoritePropertyDto = {
      code: code,
      is_favorite: !favorite,
    };
    try {
      await favoriteListingMutation.mutateAsync(data);
      setFavorite((prev) => !prev);
    } catch (error: any) {
      errorCallback?.(error);
    }
  };
  return { favorite, handleFavorite, authenticated, membership };
}
