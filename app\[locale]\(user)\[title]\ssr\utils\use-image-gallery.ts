export const AUTH_BUTTON_ID = "auth-id"
export const IMAGE_DETAIL_BUTTON_ID = "image-detail-id"
export const IMAGE_DIALOG_BUTTON_ID = "image-dialog-id"
export const SUBSCRIPTION_BUTTON_ID = "subscription-button-id"
export const UPDATE_ACTIVE_IMAGE_CAROUSEL_ID_EVENT_NAME = "update-carousel-id"
export const UPDATE_STATUS_IMAGE_CAROUSEL_DETAIL = "update-status-image-carousel-detail"
export const OPEN_IMAGE_DIALOG_STATUS = "open-image-dialog-status"
export const OPEN_SUBSCRIPTION_DIALOG = "open-subscription-dialog"

export default function propertyDetailUtils(){
  const handleOpenAuthDialog = () => {
    const authButton =  document.getElementById(AUTH_BUTTON_ID)
    authButton?.click()
  }
  const handleShareActiveImageCarousel = (activeIndex:number) => {
    const event = new CustomEvent(UPDATE_ACTIVE_IMAGE_CAROUSEL_ID_EVENT_NAME, {
      detail: {
        activeIndex
      }
    })
    window.dispatchEvent(event)
  }
  const handleSetOpenDetailImage = (isOpen?:boolean) => {
    const event = new CustomEvent(OPEN_IMAGE_DIALOG_STATUS, {
      detail: {
        isOpen
      }
    })
    window.dispatchEvent(event)
  }
  const handleOpenSubscriptionDialog = () => {
    const subscriptionButton = document.getElementById(SUBSCRIPTION_BUTTON_ID)
    subscriptionButton?.click()
  }
  const handleOpenImageDetailDialog = () => {
    const imageDetailButton = document.getElementById(IMAGE_DIALOG_BUTTON_ID)
    imageDetailButton?.click()
  }
  const handleOpenStatusImageDetailCarousel = (isOpen?:boolean) => {
    const event = new CustomEvent(UPDATE_STATUS_IMAGE_CAROUSEL_DETAIL,{
      detail: {
        isOpen
      }
    })
    window.dispatchEvent(event)
  }
  return {handleShareActiveImageCarousel, handleSetOpenDetailImage, handleOpenAuthDialog,handleOpenSubscriptionDialog,handleOpenStatusImageDetailCarousel,handleOpenImageDetailDialog}
}