"use client"
import { cn } from "@/lib/utils"
import * as m from "framer-motion/m"
import { ChevronUp } from "lucide-react"
import { useTranslations } from "next-intl"
import React, { useState } from "react"
import { LazyMotion, domAnimation } from "framer-motion"

export default function MobilePropertyActionDetail({ overview, children }: { overview: React.ReactNode, children: React.ReactNode }) {
  const t = useTranslations("seeker")
  const [open, setOpen] = useState(false)
  return <LazyMotion features={domAnimation}>
    <m.div
      className="w-full md:hidden"
    >
      <button className="pb-2 flex items-center justify-center gap-2 w-full" onClick={() => setOpen(prev => !prev)}>
        <ChevronUp width={12} className={cn(open ? "rotate-180 transition-transform duration-300" : "")} />
        <>
          {open ? t('cta.close') : t('cta.detail')}
        </>
      </button>
      <m.div
        initial={{
          height: 0,
        }}
        animate={{
          height: open ? "fit-content" : 0,

        }}
        transition={{
          duration: 0.6,
          ease: "easeOut"
        }}
        className="overflow-hidden space-y-2"
      >
        {children}
      </m.div>
      <div className="">
        {overview}
      </div>
    </m.div>
  </LazyMotion>
}