import dynamic from "next/dynamic"

const RecommendationProperties = dynamic(() => import("./recommendation-properties"), { ssr: false })
export default function ClientRecommendationProperty({ lat, lng, currency = "EUR", locale = "en", conversions, currentPropertyCode }: { lat?: string, lng?: string, currency: string, locale: string, conversions: { [key: string]: number }, currentPropertyCode: string }) {
  return <RecommendationProperties
    currentPropertyCode={currentPropertyCode}
    lat={lat}
    lng={lng}
    currency={currency}
    locale={locale || "en"}
    conversions={conversions}
  />
}