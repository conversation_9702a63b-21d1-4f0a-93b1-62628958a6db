import {
  NextFetchEvent,
  NextMiddleware,
  NextRequest,
  NextResponse,
} from "next/server";
import { MiddlewareFactory } from "./types";

const allowedOrigin = [
  "https://www.property-plaza.id",
  "https://www.property-plaza.com",
  "https://seekers.property-plaza.com",
  "https://owners.property-plaza.id",
  "https://www.properti-plaza.id",
  "http://localhost:3000",
  "http://localhost:3001",
];

export const withCors: MiddlewareFactory =
  (next: NextMiddleware) =>
  async (request: NextRequest, _next: NextFetchEvent) => {
    let nextResult = await next(request, _next);
    let response = NextResponse.next();
    const url = request.url;
    const origin = request.headers.get("origin");
    if (!url.includes("api")) {
      if (nextResult) return nextResult;
      return response;
    }
    if (origin && allowedOrigin.includes(origin)) {
      response.headers.set("Access-Control-Allow-Origin", origin);
      if (nextResult) {
        return nextResult;
      }
      return response;
    } else {
      return new Response(
        JSON.stringify({ error: "CORS not allowed for this origin" }),
        {
          status: 403,
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
    }
  };
