"use client"
import { useTranslations } from "next-intl";
import { useEmailFormSchema } from "../../reset-password/form/use-email-form.schema";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import DefaultInput from "@/components/input-form/default-input";
import { useRequestResetPassword } from "@/core/applications/mutations/auth/use-request-reset-password";
import { RequestForgetPasswordDto } from "@/core/infrastructures/auth/dto";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";

export default function EmailForm({ isDialog, isSeeker, onGoBack }: { isSeeker?: boolean, isDialog?: boolean, onGoBack?: () => void }) {
  const t = useTranslations("universal")
  const { toast } = useToast()
  const formSchema = useEmailFormSchema()
  type formSchematype = z.infer<typeof formSchema>
  const useRequestResetPasswordMutation = useRequestResetPassword()
  const router = useRouter()
  const form = useForm<formSchematype>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: ""
    }
  })
  async function onSubmit(values: z.infer<typeof formSchema>) {
    const data: RequestForgetPasswordDto = {
      email: values.email
    }
    try {
      await useRequestResetPasswordMutation.mutateAsync(data)
    } catch (error: any) {
      toast({
        title: t("error.requestForgetPassword.title"),
        description: error.response.data.message,
        variant: "destructive"
      })
    }
  }
  return <div className="w-full space-y-6">
    {useRequestResetPasswordMutation.isSuccess ?
      <div className="flex flex-col gap-6 items-center">
        <div className="space-y-2 text-center" >
          <h1 className="text-2xl font-semibold ">{t('success.requestForgotPassword.title')}</h1>
          <p className="text-neutral-500" >{t('success.requestForgotPassword.description')}</p>
        </div>
        <Button variant={"link"} onClick={() => router.push("/")} asChild>
          {t("cta.goBack")}
        </Button>
      </div>
      :
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="space-y-2 text-center">
            <h1 className="text-2xl font-semibold text-center">{t("form.title.resetPassword")}</h1>
            <p className="text-neutral-500">{t('form.description.resetPassword')}</p>
          </div>
          <DefaultInput
            type="email"
            form={form}
            name="email"
            variant="float"
            label={t("form.label.email")}
            labelClassName="text-xs text-seekers-text-light font-normal"
            placeholder={""}
          />
          <div className="space-y-2">
            {
              isSeeker ? <></> :
                <Button className="w-full" variant={"default-seekers"} loading={useRequestResetPasswordMutation.isPending}>
                  {t("cta.sendResetPassword")}
                </Button>
            }
            {
              isDialog ?
                <Button type="button" className="w-full text-neutral-600" variant={"link"} onClick={() => onGoBack?.()}>
                  {t('cta.goBack')}
                </Button>
                :
                <Button type="button" variant={"link"} onClick={() => router.back()} className="w-full text-neutral-600">
                  {t('cta.goBack')}
                </Button>
            }
          </div>
        </form>
      </Form>
    }
  </div>
}