import { DateType } from "@/core/domain/listing/listing";
import { useTranslations } from "next-intl";

export default function DateFormatter(date: DateType) {
  const t = useTranslations()
  switch (date) {
    case "DAY":
      return t('listing.pricing.durationContract.daily')
    case "MONTH":
      return t('listing.pricing.durationContract.monthly')
    case "WEEK":
      return t('listing.pricing.durationContract.week')
    case "YEAR":
      return t('listing.pricing.durationContract.yearly')
    default:
      return date
  }
}