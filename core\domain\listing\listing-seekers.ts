import { ItemWithSuffix } from "../utils/utils";
import {
  Availability,
  BasicInformation,
  Feature,
  ListingImage,
  ListingType,
  Location,
} from "./listing";

export const listingCategory = {
  villas: "VILLA",
  apartment: "APARTMENT",
  rooms: "ROOM",
  commercialSpace: "COMMERCIAL_SPACE",
  cafeOrRestaurants: "CAFE_RESTAURANT",
  offices: "OFFICE",
  shops: "SHOP",
  shellAndCore: "SHELL_CORE",
  lands: "LAND",
  guestHouse: "GUESTHOUSE",
  homestay: "HOMESTAY",
  ruko: "RUKO",
  villa: "VILLA",
};
export type ListingCategory =
  (typeof listingCategory)[keyof typeof listingCategory];
export const listingViewType = {
  all: "ANY",
  mountain: "MOUNTAIN",
  ocean: "OCEAN",
  ricefield: "RICEFIELD",
  jungle: "JUNGLE",
};

export interface ListingImageSeekers {
  id: string;
  image: string;
  isHighlight: boolean;
}
export interface ListingListSeekers {
  isFavorite?: boolean;
  code: string;
  title: string;
  location: string;
  geolocation: [number, number];
  price: number;
  thumbnail: ListingImageSeekers[];
  sellingPoint: SellingPoint[];
  listingDetail: ListingDetail;
  availability: {
    availableAt: string;
    minDuration: ItemWithSuffix | null;
    maxDuration: ItemWithSuffix | null;
    type: string;
  };
  category?: string;
  status: string;
}

export interface SellingPoint {
  suffix: string | null;
  title: string;
  value: string;
}
export interface ListingDetail {
  landSize: number;
  buildingSize: number;
  gardenSize: number;
  cascoStatus: boolean;
  bathRoom: ItemWithSuffix;
  bedRoom: ItemWithSuffix;
}

export const seekersListingFilterType = {
  anything: "ANY",
  placeToLive: "PLACE_TO_LIVE",
  business: "BUSINESS",
  land: "LAND",
} as const;

export type SeekerListingFilterType =
  (typeof seekersListingFilterType)[keyof typeof seekersListingFilterType];

export interface FilterPriceDistribution {
  price: string;
  amount: string;
}

export interface ListingSeekerDetail {
  id: string;
  propertyId: string;
  title: string;
  excerpt: string;
  description: string;
  detail: BasicInformation;
  location: Location;
  availability: Availability;
  features: Feature;
  images: ListingImage[];
  status: ListingType;
  owner: {
    name: string;
    image: string;
    code: string;
  } | null;
  middleman: {
    name: string;
    image: string;
    code: string;
  } | null;
  isFavorite?: boolean;
  chatCount: number;
}

export interface RangeFilter {
  min: number;
  max: number;
}
export interface FilterOption {
  title: string;
  value: string;
}

export interface FilterParameter {
  priceRange: RangeFilter;
  landSizeRange: RangeFilter;
  buildingSizeRange: RangeFilter;
  gardenSizeRange: RangeFilter;
  parkingOptions: FilterOption[];
  poolOptions: FilterOption[];
  livingOptions: FilterOption[];
  furnishingOptions: FilterOption[];
}

export const SellingPointList = {
  plumbing: "PLUMBING",
  subleaseAllowed: "SUBLEASE_ALLOWED",
  balcony: "BALCONY",
  gazebo: "GAZEBO",
  recentlyRenovated: "RECENTLY_RENOVATED",
  airCondition: "AIR_CONDITION",
  constructionNearby: "CONSTRUCTION_NEARBY",
  rooftopTerrace: "ROOFTOP_TERRACE",
  terrace: "TERRACE",
  petAllowed: "PET_ALLOWED",
  garden: "GARDEN_BACKYARD",
  bathub: "BATHUB",
};

export const businessBuildingSize = {
  small: { min: 1, max: 300, key: "small" },
  medium: { min: 301, max: 1000, key: "medium" },
  large: { min: 1001, max: 100_000, key: "large" },
};
export type BusinessBuildingSize = keyof typeof businessBuildingSize;
