import { postTwoFactorAuthenticationCode } from "./api";
import { TwoFactorAuthenticationCodeDto } from "./dto";
import { transformTwoFactorAuthenticationCode } from "./transform";

export async function getTwoFactorAuthenticationCodeService(
  data: TwoFactorAuthenticationCodeDto
) {
  try {
    const request = await postTwoFactorAuthenticationCode(data);
    return {
      data: request.data.data,
    };
  } catch (e: any) {
    return {
      error: e.data.error ?? "An unknown error occurred",
    };
  }
}
