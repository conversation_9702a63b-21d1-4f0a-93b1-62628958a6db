"use client"
import { useState } from "react";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Mail, CheckCircle } from "lucide-react";
import EmailCaptureForm from "./email-capture/email-capture.form";


export default function GuideEmailCapture() {
  const t = useTranslations("guide");
  const [isSuccess, setIsSuccess] = useState(false);

  if (isSuccess) {
    return (
      <section id="email-capture-section" className="py-20 bg-gradient-to-br from-seekers-primary-lightest to-neutral-lightest">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-seekers-foreground/50 rounded-3xl p-12 shadow-xl">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="h-10 w-10 text-green-600" />
            </div>

            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {t("guide.emailCapture.checkEmaill")}
            </h2>

            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              {t.rich("guide.emailCapture.checkEmaillDescription", {
                strong: (chunk) => <strong>{chunk}</strong>
              })}
            </p>

            <div className="bg-gray-50 rounded-2xl p-6 mb-8">
              <h3 className="font-semibold text-gray-900 mb-2">

                {t('misc.whatsNext')}
              </h3>
              <p className="text-gray-600 text-sm">
                {t('guide.emailCapture.nextStepDescription')}
              </p>
            </div>

            <Button
              onClick={() => window.open('/', '_blank')}
              size="lg"
              className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-4 text-lg font-semibold rounded-lg"
            >
              {t('cta.browsePropertyNow')}
            </Button>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="email-capture-section" className="py-20 bg-gradient-to-br from-seekers-primary-lightest to-neutral-lightest">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-seekers-foreground/50 rounded-3xl p-8 md:p-12 shadow-xl">
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-seekers-primary-lighter rounded-full flex items-center justify-center mx-auto mb-6">
              <Mail className="h-8 w-8 text-seekers-primary" />
            </div>

            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              {t("guide.emailCapture.title")}
            </h2>

            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {t("guide.emailCapture.description")}

            </p>
          </div>
          <EmailCaptureForm setSuccess={setIsSuccess} />
        </div>
      </div>
    </section>
  );
}
