"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Carousel, CarouselContent, CarouselDots, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import { Skeleton } from "@/components/ui/skeleton"
import { usePostFavoriteListing } from "@/core/applications/mutations/listing/use-post-favorite-listing"
import { listingCategory, ListingListSeekers } from "@/core/domain/listing/listing-seekers"
import { PostFavoritePropertyDto } from "@/core/infrastructures/listing/dto"
import { imagePlaceholder } from "@/lib/constanta/image-placeholder"
import { cn, formatCurrency, uppercaseFirstLetter } from "@/lib/utils"
import { useSeekersSettingsStore } from "@/stores/seekers-settings.store"
import { SizeIcon } from "@radix-ui/react-icons"
import { BathIcon, BedDouble, Heart, MapPin } from "lucide-react"
import { useLocale, useTranslations } from "next-intl"
import Image from "next/image"
import React, { ComponentP<PERSON>, create<PERSON>ontext, useContext, forwardRef, useState, useEffect } from "react"
import Cookies from "js-cookie";
import { ACCESS_TOKEN } from "@/lib/constanta/constant"
import SellingPointFormatter from "./selling-point-formatter"
import { useUserStore } from "@/stores/user.store"
import SeekerAuthDialog from "../(auth)/seekers-auth-dialog"
import { isListingActive, ListingContractType } from "@/core/domain/listing/listing"
import { useSeekersPriceHelper } from "@/hooks/use-seekers-price-helper"
import BuildingSizeIcon from "@/components/icons/property-detail/Building Size.svg"
import TooltipWrapper from "@/components/tooltop-wrapper/tooltip-wrapper"
import { noLoginPlanUrl, plansUrl } from "@/lib/constanta/route"
import Link from "next/link"
import { packages } from "@/core/domain/subscription/subscription"
import { useToast } from "@/hooks/use-toast"

interface ListingContenxt {
  listing: ListingListSeekers,
  setClientFavoriteListing?: (val: boolean) => void // only update favorite status on client to prevent re-fetching data
  handleOpenListing: () => void,
  conversion: { [key: string]: number }
}

const listingContext = createContext<ListingContenxt | undefined>(undefined)

const useListingContext = () => {
  const context = useContext(listingContext)
  if (!context) {
    throw new Error("useListingContext must be used within a Listings")
  }
  return context
}
export default function ListingItem({ data, maxImage, conversion, forceLazyloading, disabledSubscriptionAction }: { data: ListingListSeekers, disabledSubscriptionAction?: boolean, maxImage?: number, forceLazyloading?: boolean, conversion: { [key: string]: number } }) {
  return <ListingWrapper data={
    {
      ...data,
      thumbnail: maxImage ? data.thumbnail.slice(0, maxImage) : data.thumbnail,

    }}
    conversion={conversion}
  >
    <ListingImage forceLazyloading={forceLazyloading} disableSubscriptionAction={disabledSubscriptionAction} />
    <div className="space-y-2 px-0.5">
      <div>
        <ListingTitle className="line-clamp-1" />
        <ListingLocation />
      </div>
      <ListingPrice />
    </div>
  </ListingWrapper>
}


interface ListingWrapperProps extends ComponentProps<"div"> {
  children: React.ReactNode;
  data: ListingListSeekers;
  className?: string;
  handleFavoriteListion?: (val: boolean) => void,
  conversion: { [key: string]: number }

}
export const ListingWrapper = forwardRef<HTMLDivElement, ListingWrapperProps>(
  ({ children, data, className, conversion, ...rest }, ref) => {
    const [listings, setListing] = useState<ListingListSeekers>(data)
    const handleSetClientFavorite = (val: boolean) => {
      setListing(prev => ({ ...prev, isFavorite: val }))
      rest.handleFavoriteListion?.(val)
    }
    useEffect(() => {
      setListing(data)
    }, [data])
    const handleClickListing = () => {
      window.open(`/${data.title.replace(/\W+/g, "-")}?code=${data.code}`)
    }
    return (
      <listingContext.Provider value={{
        listing: listings,
        setClientFavoriteListing: handleSetClientFavorite,
        handleOpenListing: handleClickListing,
        conversion: conversion
      }}>
        <div {...rest} ref={ref} className={cn("relative w-full space-y-2 isolate cursor-pointer", className)} >
          {children}
        </div>
      </listingContext.Provider>
    );
  }
);
ListingWrapper.displayName = "ListingWrapper";

function ListingHeader({
  isFavorite,
  code, size = "small",
  extraAction,
  updateClientFavorite,
  activeListing = false,
  allowFavoritedWhileInactive = false
}: {
  isFavorite?: boolean,
  code: string,
  size?: "large" | "small",
  extraAction?: React.ReactNode,
  updateClientFavorite: (val: boolean) => void,
  allowFavoritedWhileInactive?: boolean,
  activeListing?: boolean

}) {
  const favoriteListingMutation = usePostFavoriteListing()
  const locale = useLocale()
  const t = useTranslations("seeker")
  const bearer = Cookies.get(ACCESS_TOKEN)
  const { role, seekers } = useUserStore(state => state)
  const buttonClassName = cn("z-10  rounded-full h-[26px] w-[26px] hover:bg-transparent hover:scale-110 transition-transform duration-100 ease-linear", size == "small" ? "w-[24px] h-[24px]" : "w-[26px] h-[26px]")
  const iconClassName = cn("text-white", size == "small" ? "!w-4 !h-4" : "!w-5 !h-5")
  const { toast } = useToast()
  const handleFavorite = async () => {
    if (!bearer && role !== "SEEKER") return
    if (!activeListing && !allowFavoritedWhileInactive) return
    if (seekers.accounts.membership === "Free") {
      toast({
        title: t("misc.subscibePropgram.favorite.title"),
        description: <>
          {t('misc.subscibePropgram.favorite.description')}
          <Button asChild variant={"link"} size={"sm"} className="p-0 text-seekers-primary h-fit w-fit underline">
            <Link href={seekers.email ? plansUrl : noLoginPlanUrl} hrefLang={locale}>{t('cta.subscribe')}</Link>
          </Button>
        </>
      })
      return
    }
    const data: PostFavoritePropertyDto = {
      code: code,
      is_favorite: !isFavorite
    }
    try {
      updateClientFavorite(!isFavorite)
      await favoriteListingMutation.mutateAsync(data)
    } catch (error: any) {

    }
  }

  return <div className="w-full py-3 px-2.5 pr-3 flex justify-end items-center gap-2">
    {bearer && role == "SEEKER" ?
      <Button
        size={"icon"}
        onClick={(e) => {
          e.stopPropagation()
          handleFavorite()
        }}
        className={buttonClassName}
        variant={"ghost"}>
        <Heart className={iconClassName} fill={isFavorite ? "red" : "#707070"} fillOpacity={isFavorite ? 1 : 0.5} />
      </Button>
      :
      <SeekerAuthDialog customTrigger={<Button
        size={"icon"}
        className={buttonClassName}
        variant={"ghost"}>
        <Heart className={iconClassName} fill={"#707070"} fillOpacity={0.5} />
      </Button>} />
    }
    {extraAction}
  </div >
}

export function ListingImage({ heartSize = "small", containerClassName, extraHeaderAction, allowFavoriteWhileInactive = false, forceLazyloading = false, disableSubscriptionAction }: { heartSize?: "small" | "large", containerClassName?: string, extraHeaderAction?: React.ReactNode, allowFavoriteWhileInactive?: boolean, forceLazyloading?: boolean, disableSubscriptionAction?: boolean }) {
  const { listing, setClientFavoriteListing, handleOpenListing } = useListingContext()
  const t = useTranslations("seeker")
  const { seekers } = useUserStore(state => state)
  const locale = useLocale()
  return <Carousel opts={{
    loop: seekers.accounts.membership == packages.free ? false : true,
    active: isListingActive(listing.status) && (listing.thumbnail.length > 1 && !disableSubscriptionAction),
  }}
    className={cn("group isolate w-full aspect-[4/3] relative rounded-xl overflow-hidden", containerClassName)}>
    <ListingHeader
      updateClientFavorite={setClientFavoriteListing!}
      isFavorite={listing.isFavorite}
      code={listing.code}
      size={heartSize}
      extraAction={extraHeaderAction}
      activeListing={isListingActive(listing.status)}
      allowFavoritedWhileInactive={allowFavoriteWhileInactive}
    />
    {
      !isListingActive(listing.status) && <div
        onClick={() => handleOpenListing()}
        className=" absolute top-0 left-0 rounded-xl w-full h-full -z-10 bg-slate-800/30 flex flex-col items-center justify-center">
        <p className="text-white font-semibold">{t("misc.notAvailable")}</p>
      </div>
    }
    <CarouselContent className="absolute top-0 left-0 w-full h-full ml-0 -z-20">
      {listing.thumbnail.map((item, idx) => <CarouselItem key={item.id} className="relative"
        onClick={(e) => {
          e.stopPropagation()
          handleOpenListing()
        }}>
        <div className="absolute inset-0 z-10 pointer-events-none watermark-overlay" />

        <Image
          src={item.image}
          alt={`${listing.title}`}
          title={listing.title}
          fill
          sizes="300px"
          priority={(idx == 0 && !forceLazyloading) ? true : false}
          loading={(idx == 0 || !forceLazyloading) ? "eager" : "lazy"}
          style={{ objectFit: 'cover' }}
          blurDataURL={imagePlaceholder}
          placeholder="blur"
          quality={10}
        />
      </CarouselItem>
      )}
      {seekers.accounts.membership == packages.free && !disableSubscriptionAction && <CarouselItem
        className="relative isolate"
        onClick={(e) => {
          e.stopPropagation()
        }}>
        <Image
          className="-z-10 brightness-50 blur-md"
          src={imagePlaceholder}
          alt=""
          fill
          sizes="300px"
          loading="lazy"
          blurDataURL={imagePlaceholder}
          placeholder="blur" />
        <div className="z-10 text-white absolute top-1/2 left-1/2 text-center flex flex-col items-center -translate-x-1/2 -translate-y-1/2 min-w-[200px]">
          <p className="text-center">
            {t('misc.subscibePropgram.detailPage.description')} {" "}
          </p>
          <Button asChild variant={"link"} size={"sm"} className="p-0 h-fit w-fit text-white underline">
            <Link href={noLoginPlanUrl} hrefLang={locale}>{t('cta.subscribe')}</Link>
          </Button>
        </div>
      </CarouselItem>
      }
    </CarouselContent>
    {listing.thumbnail.length <= 1 || !isListingActive(listing.status) ? <></> :
      <div className="flex absolute top-1/2 -translate-y-1/2 left-0 w-full justify-between px-3">
        <CarouselPrevious className="left-3 !opacity-0 group-hover:!opacity-100 transition duration-75 ease-in" />
        <CarouselNext className="right-3 !opacity-0 group-hover:!opacity-100 transition duration-75 ease-in" />
      </div>
    }
    <div className="flex absolute bottom-4 left-0 w-full items-center justify-center">
      <CarouselDots carouselDotClassName="hover:bg-seekers-primary" className="" />
    </div>
    <div
      className="absolute w-full pointer-events-none h-full top-0 left-0 bg-gradient-to-b from-neutral-900/40 via-neutral-900/5 to-neutral-100/0 -z-10 group-hover:opacity-0  transition-all duration-100 ease-in-out">

    </div>
  </Carousel>
}

export function ListingTitle({ className }: { className?: string }) {
  const { listing, handleOpenListing } = useListingContext()
  return <h3 className={cn("font-semibold text-seekers-text text-base line-clamp-1", className)} onClick={e => {
    e.stopPropagation()
    handleOpenListing()
  }}>{listing.title}</h3>
}
export function ListingLocation({ className }: { className?: string }) {
  const { listing, handleOpenListing } = useListingContext()
  return <div className={cn("flex items-center text-xs gap-1 text-seekers-text-light font-medium", className)} onClick={e => {
    e.stopPropagation()
    handleOpenListing()
  }} ><MapPin className="w-4 h-4" /> {listing.location} </div>
}
export function ListingPrice() {
  const { currency } = useSeekersSettingsStore()
  const { listing, handleOpenListing, conversion } = useListingContext()

  const { startWord, formattedPrice, suffix } = useSeekersPriceHelper(
    listing.price,
    listing.availability.type as ListingContractType,
    listing.availability.minDuration || undefined,
    listing.availability.maxDuration || undefined
  )
  return <p className=" text-base text-seekers-text font-medium " onClick={e => {
    e.stopPropagation()
    handleOpenListing()
  }}>
    {/* This check if there's minimum duration, so it will become rent */}

    <span className="text-sm font-medium text-seekers-text-lighter">
      {uppercaseFirstLetter(startWord)}
    </span>
    {" "}
    {formatCurrency(formattedPrice * (conversion[currency] || 1), currency, "en-US")}
    {" "}
    <span className="text-xs text-seekers-text-lighter">{suffix}</span>

  </p>
}

export function ListingLoader() {
  return <div className="w-full space-y-2">
    <Skeleton className="w-full aspect-[4/3]" />
    <div className="space-y-1 px-0.5">
      <Skeleton className="w-full h-8" />
      <Skeleton className="w-full h-4" />
      <Skeleton className="w-full h-4" />
    </div>
  </div>
}

export function ListingSellingPoint({ className }: { className?: string }) {
  const t = useTranslations("seeker")
  const { listing, handleOpenListing } = useListingContext()
  const roomsAndCommercialSpaceCategory = [listingCategory.rooms, listingCategory.commercialSpace, listingCategory.cafeOrRestaurants, listingCategory.offices, listingCategory.shops, listingCategory.shellAndCore]
  const livingSpaceCategory = [listingCategory.villa, listingCategory.apartment, listingCategory.homestay, listingCategory.guestHouse]
  return <div className={cn("flex gap-2 text-xs font-normal h-fit !mt-0 text-seekers-text", className)} onClick={e => {
    e.stopPropagation()
    handleOpenListing()
  }}>
    {roomsAndCommercialSpaceCategory.includes(listing.category || "") && <>
      <TooltipWrapper
        trigger={
          <div className="flex gap-1 items-end">
            <Image loading="lazy" src={BuildingSizeIcon || ""} alt=""
              width={16} height={16} className={"w-4 h-4"}
              aria-label={t('listing.feature.additionalFeature.buildingSize')} />
            <span>{listing.listingDetail.buildingSize}</span>
            <span>
              m
              <span className="align-super text-[10px]">2</span>
            </span>
          </div>
        }
        content={<p>{t('listing.feature.additionalFeature.buildingSize')}</p>}
        contentClassName="text-seekers-primary p-2 text-sm"
      />

    </>}
    {livingSpaceCategory.includes(listing.category || "") && <>
      <TooltipWrapper
        trigger={
          <div className="flex gap-1 items-end cursor-pointer">
            <BedDouble className="w-4 h-4" strokeWidth={1} />
            <span>{listing.listingDetail.bedRoom.value}</span>
          </div>
        }
        content={<p>{t('listing.feature.additionalFeature.bedroom')}</p>}
        contentClassName="text-seekers-primary p-2 text-sm"
      />
      <TooltipWrapper
        trigger={
          <div className="flex gap-1 items-end cursor-pointer">
            <BathIcon className="w-4 h-4" strokeWidth={1} />
            <span>{listing.listingDetail.bathRoom.value}</span>
          </div>
        }
        content={<p>{t('listing.feature.additionalFeature.bathroom')}</p>}
        contentClassName="text-seekers-primary p-2 text-sm"
      />

    </>
    }
    {(listing.category !== listingCategory.lands && listing.sellingPoint?.length > 0) && <>
      <div className="flex gap-1 items-end">
        <SellingPointFormatter {...listing.sellingPoint[0]} />
      </div>
    </>
    }
    <TooltipWrapper
      trigger={
        <div className="flex gap-1 items-end cursor-pointer">
          <SizeIcon className="w-4 h-4" strokeWidth={1.5} />
          <p>
            {listing.listingDetail.landSize || ""} {" "}
            <span>
              m
              <span className="align-super text-[10px]">2</span>
            </span>
          </p>
        </div>
      }
      content={<p>{t('listing.feature.additionalFeature.land')}</p>}
      contentClassName="text-seekers-primary p-2 text-sm"
    />
  </div >
}
