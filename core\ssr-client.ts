"use server"
import { ACCESS_TOKEN } from "@/lib/constanta/constant";
import { BasePaginationRequest, FetchhMethod } from "./utils/types"
import { cookies } from 'next/headers';


type Meta = BasePaginationRequest
interface ApiError {
    status: number,
    name:string,
    message:string,
    details: Record<string,unknown>
  }
export interface ApiResponse<T> {
  data: T | null,
  error?: ApiError,
  meta?:Meta
}

export default async function ssrApiClient<T>(
  url:string, 
  fetchMethod: FetchhMethod,
  options?:RequestInit):
  Promise<ApiResponse<T>>{
  const cookieStore = cookies();
  const token = cookieStore.get(ACCESS_TOKEN)?.value;

  try {
    const res = await fetch(url,{
      method: fetchMethod,
      headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      },
      ...options
    })
    if(!res.ok){
      return {
        data: null,
        meta:undefined,
        error: {
          status: res.status,
          name: res.statusText,
          message: await res.text() || "Unexpected error",
          details: {}
        }
      }
    }

    const data = await res.json()



    if(data.error){
      return {
        data: null,
        meta:undefined,
        error: data.error as ApiError
      }
    }
    return {
      data: data.data,
      meta: data.meta as Meta,
      error: undefined
    }

  } catch(err) {
    const error = err as Error
    return {
      data: null,
      meta: undefined,
      error: {
        status: 500,
        details: {
          cause:error.cause
        },
        message: error.message,
        name: error.name
      }
    }
  } 
  
}