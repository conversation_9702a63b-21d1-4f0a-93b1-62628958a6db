import { MessageItem, MessageText } from "@/core/domain/messages/messages";
import { MessageDto, MessageTextDto } from "./dto";
import moment from "moment";

export function transformMessage(dto: MessageDto[]): MessageItem[] {
  const messages = dto.map((item) => {
    const lastMessage: MessageTextDto | undefined = item.messages[0];
    if (!lastMessage) return undefined;
    return {
      code: item.code,
      category: item.category,
      roomId: item.participants.room_id,
      lastMessages: {
        createdAt: lastMessage?.created_at || item.created_at,
        displayAs: lastMessage?.display_as || "",
        displayName: lastMessage?.display_name || "",
        text: lastMessage?.text || "",
        isRead: lastMessage?.is_read || false,
        isSent: lastMessage?.is_send || false,
        id: lastMessage?.id || "",
        code: lastMessage?.code || "",
      },
      participant: {
        email: item.participants.info?.email || "",
        fullName: item.participants.info?.display_name || "",
        phoneNumber: item.participants.info?.phone_number || "",
        image: item.participants.info.image || "",
        id: item.participants.info?.id || "",
        category: item.category,
        status: item.status,
        property: {
          title: item.ref_data?.title || undefined,
          image: item.ref_data?.images[0].image || undefined,
        },
        otherProperty: [],
      },
      status: item.status,
      updatedAt: item.updated_at,
    };
  });
  const filteredMessages = messages.filter((item) => item !== undefined);
  const sortedMessage = filteredMessages.sort(
    (a, b) =>
      moment(b.lastMessages.createdAt).unix() -
      moment(a.lastMessages.createdAt).unix()
  );
  return sortedMessage;
}

export function transformMessageText(dto: MessageTextDto): MessageText {
  return {
    createdAt: dto.created_at,
    displayAs: dto.display_as,
    displayName: dto.display_name,
    isRead: dto.is_read,
    isSent: dto.is_send,
    text: dto.text,
    id: dto.id,
    code: dto.code,
  };
}

export function transformMessageDetail(dto: MessageDto): MessageItem {
  console.log(dto.messages);
  const lastMessage = dto.messages[dto.messages?.length - 1] || undefined;
  const messages = dto.messages.map((item) => ({
    createdAt: item.created_at,
    displayAs: item.display_as,
    displayName: item.display_name,
    isRead: item.is_read,
    isSent: item.is_send,
    text: item.text,
    id: item.id,
    code: item.code,
  }));
  const moreProperty = dto.ref_data?.extended_list?.map((item) => ({
    id: item.code,
    image: item.images[0]?.image || "",
    title: item.title,
  }));
  return {
    code: dto.code,
    category: dto.category,
    roomId: dto.participants.room_id,
    lastMessages: {
      createdAt: lastMessage.created_at,
      displayAs: lastMessage.display_as,
      displayName: lastMessage.display_name,
      text: lastMessage.text,
      isRead: lastMessage.is_read,
      isSent: lastMessage.is_send,
      id: lastMessage.id,
      code: lastMessage.code || "",
    },
    participant: {
      email: dto.participants.info?.email || "",
      fullName: dto.participants.info?.display_name || "",
      phoneNumber: dto.participants.info?.phone_number || "",
      image: dto.participants.info?.image || "",
      id: dto.participants.info?.id || "",
      category: dto.category,
      status: dto.status,
      property: {
        id: dto.ref_data?.code || "",
        image: dto.ref_data?.images[0]?.image || "",
        title: dto.ref_data?.title || "",
      },
      moreProperty: moreProperty || [],
    },

    allMessages: messages,
    updatedAt: dto.updated_at,
  };
}
