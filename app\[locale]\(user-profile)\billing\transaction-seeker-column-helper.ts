import { SeekerTransaction } from "@/core/domain/transaction/transaction";
import { useTranslations } from "next-intl";

export function TransactionSeekerColumnHelper(value: keyof SeekerTransaction) {
  const t = useTranslations("seeker");
  switch (value) {
    case "amount":
      return t("transaction.dataTable.amount");
    case "date":
      return t("transaction.dataTable.transactionDate ");
    case "downloadUrl":
      return t("transaction.dataTable.transactionId");
    case "invoiceNumber":
      return t("transaction.dataTable.invoiceNumber ");
    case "nextBilling":
      return t("transaction.dataTable.nextBillingDate");
    case "plan":
      return t("transaction.dataTable.plan");
    default:
      return value;
  }
}
