import { useTranslations } from "next-intl";
import { z } from "zod";

export function useEditBillingSchema() {
  const t = useTranslations("seeker");
  const formSchema = z.object({
    addressOne: z.string({
      message: t("form.utility.fieldRequired", {
        field: t("form.field.address"),
      }),
    }),
    addressTwo: z.string().optional(),
    name: z.string({
      message: t("form.utility.fieldRequired", {
        field: t("form.field.name"),
      }),
    }),
    city: z.string({
      message: t("form.utility.fieldRequired", {
        field: t("form.field.city"),
      }),
    }),
    state: z.string({
      message: t("form.utility.fieldRequired", {
        field: t("form.field.state"),
      }),
    }),
    country: z.string({
      message: t("form.utility.fieldRequired", {
        field: t("form.field.country"),
      }),
    }),
    postalCode: z.string({
      message: t("form.utility.fieldRequired", {
        field: t("form.field.postalCode"),
      }),
    }),
  });
  return formSchema;
}
