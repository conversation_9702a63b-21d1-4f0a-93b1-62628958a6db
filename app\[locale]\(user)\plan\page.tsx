import DefaultLayoutContent from "@/components/seekers-content-layout/default-layout-content";
import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api";
import { getAllSubscriptionPackagesService } from "@/core/infrastructures/subscription/service";
import { getLocale, getTranslations } from "next-intl/server";
import SubscriptionContent from "../../(user-profile)/subscription/content";
import { Subscription } from "@/core/domain/subscription/subscription";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Metadata } from "next";
import { noLoginPlanUrl } from "@/lib/constanta/route";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("seeker")
  const locale = await getLocale()
  return {
    title: t("metadata.subsriptionPlan.title"),
    description: t("metadata.subsriptionPlan.description"),
    alternates: {
      canonical: "https://property-plaza.com/" + locale + noLoginPlanUrl,
      languages: {
        en: process.env.USER_DOMAIN + "en" + noLoginPlanUrl,
        id: process.env.USER_DOMAIN + "id" + noLoginPlanUrl,
        "x-default": process.env.USER_DOMAIN + "en" + noLoginPlanUrl,
      },
    },
    openGraph: {
      title: "Join Property Plaza Plan | Get Exclusive Verified Properties in Bali",
      description:
        "Subscribe to Property Plaza and enjoy verified listings, early property alerts, and secure property search in Bali.",
      url: `https://www.property-plaza.com/${locale}/plan`,
      siteName: "Property Plaza",
      locale: locale === "en" ? "en_US" : "id-ID",
      type: "website",
    },
    robots: {
      index: true,
      follow: true
    }
  }
}

export default async function SubscriptionPlan() {
  const conversionRates = await getCurrencyConversion("EUR")
  const subscriptionPackages = await getAllSubscriptionPackagesService()
  const data = subscriptionPackages.data
  const t = await getTranslations("seeker")
  return <MainContentLayout>

    <div className="space-y-1 my-12">
      <h1 className="capitalize text-seekers-text text-2xl font-bold tracking-[0.5%]">{t('plan.title')}</h1>
      <p className=" text-seekers-text-light text-base font-semibold tracking-[0.5%]">{t('plan.description')}</p>
    </div>
    <SubscriptionContent SubscriptionPackages={data as Subscription[] || []} conversionRate={conversionRates.data} />
  </MainContentLayout>
}