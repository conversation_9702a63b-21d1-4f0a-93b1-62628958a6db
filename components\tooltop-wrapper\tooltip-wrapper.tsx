import React from "react";
import { <PERSON><PERSON><PERSON>, Too<PERSON><PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from "../ui/tooltip";

export default function TooltipWrapper({ content, trigger, contentClassName }: { trigger: React.ReactNode, content: React.ReactNode, contentClassName?: string }) {
  return <TooltipProvider delayDuration={100}>
    <Tooltip>
      <TooltipTrigger asChild>
        {trigger}
      </TooltipTrigger>
      <TooltipContent className={contentClassName}>
        {content}
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
}