"use client"
import { GoogleReCaptchaProvider, GoogleReCaptcha } from "react-google-recaptcha-v3";
export default function RecaptchaWrapper({ refreshCaptcha, setToken }: { refreshCaptcha: boolean, setToken: (val: string) => void }) {
  return <GoogleReCaptchaProvider reCaptchaKey={process.env.NEXT_PUBLIC_RECAPTCHA_KEY || ""}>
    <GoogleReCaptcha
      onVerify={setToken}
      refreshReCaptcha={refreshCaptcha}
    />
  </GoogleReCaptchaProvider>
}