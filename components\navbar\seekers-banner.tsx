"use client"
import { usePathname } from "next/navigation";
import MainContentLayout from "../seekers-content-layout/main-content-layout";
import { useTranslations } from "next-intl";
import { Carousel, CarouselContent, CarouselItem } from "../ui/carousel";
import Autoplay from 'embla-carousel-autoplay';
import { BadgeCheck, Fan } from "lucide-react";


export default function SeekersBanner() {
  const autoplay = Autoplay({
    delay: 3000,
    stopOnInteraction: false
  })
  const t = useTranslations("seeker")
  const pathname = usePathname()
  return <>
    {
      pathname.includes("/s/") ? <></> : <></>
    }
    <div className="w-full py-3 bg-[#F7ECDC]">
      <MainContentLayout className="md:hidden">
        <div className="w-full md:hidden">
          <Carousel opts={{
            active: true,
            loop: true,

          }}
            plugins={[autoplay]}
          >
            <CarouselContent className="text-seekers-primary">
              <CarouselItem className="inline-flex gap-2 items-center">
                <Fan className="!w-5 !h-5" />
                <p className="font-semibold text-xs">
                  {t('banner.seekers.discoverDreamHome.title')}
                </p>
              </CarouselItem>
              <CarouselItem className="inline-flex gap-2 items-center">
                <BadgeCheck className="!w-5 !h-5" />
                <p className=" font-semibold text-xs">
                  {t('banner.seekers.connectToPropertyOwner.title')}
                </p>
              </CarouselItem>
            </CarouselContent>
          </Carousel>
        </div>

      </MainContentLayout>
      <MainContentLayout className="hidden md:block text-seekers-primary">
        <div className="hidden md:flex gap-4 md:justify-between">
          <div className="inline-flex gap-2 items-center">
            <p className="font-semibold text-xs">
              {t('banner.seekers.discoverDreamHome.title')}
            </p>
            <Fan className="!w-4 !h-4" />
          </div>
          <div className="inline-flex gap-2 items-center">
            <BadgeCheck className="!w-4 !h-4" />
            <p className="font-semibold text-xs">
              {t('banner.seekers.connectToPropertyOwner.title')}
            </p>
          </div>
        </div>
      </MainContentLayout>
    </div>
  </>
}