import { Metadata } from "next";
import dynamic from 'next/dynamic';
import { getLocale, getTranslations } from "next-intl/server";
import { getHomepageSeekersListingService } from "@/core/infrastructures/listing/service";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import PropertiesContent from "./(listings)/ssr/properties-content";
import { BaseMetadataProps } from "@/types/base";
import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api";
import ClientPage from "./client-page";
import ClientHowItWorksContent from "./(listings)/client-how-it-works-content";

const BlogContent = dynamic(() => import("./(listings)/ssr/blog-content"))
const SeekerFaQContent = dynamic(() => import("./(listings)/faq/content"))
const SeekerSeo = dynamic(() => import("@/components/footer/seeker-seo"))
const CategoryContent = dynamic(() => import("./(listings)/category-content"))

export async function generateMetadata({ params, searchParams }: BaseMetadataProps<{}>): Promise<Metadata> {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const t = await getTranslations("seeker")
  const locale = await getLocale()
  return {
    title: t('metadata.rootLayout.title'),
    description: t('metadata.rootLayout.description'),
    alternates: {
      canonical: "https://property-plaza.com/" + locale,
      languages: {
        "id": process.env.USER_DOMAIN + "id",
        "en": process.env.USER_DOMAIN + "en",
        "x-default": process.env.USER_DOMAIN + "en",
      },
    },
    openGraph: {
      images: [
        {
          url: "/og.png", // MUST be a full URL like "https://cdn.example.com/img.jpg"
          width: 1200,
          height: 630,
          alt: "Property Plaza",
        }
      ],
      type: "article",
      url: process.env.USER_DOMAIN || "" + locale
    },
  }
}

export default async function Home({ searchParams }: { searchParams: { [key: string]: string | undefined } }) {
  const { at, status_code, ma } = searchParams
  const t = await getTranslations("seeker")

  const getHomepageListing = await getHomepageSeekersListingService()
  const currencyConversion = await getCurrencyConversion()
  const conversion = currencyConversion.data
  return (<>
    <h1 className="sr-only">{t('srOnly.FindBestPropertyOnBali')}</h1>
    <div className="min-h-screen space-y-12 mt-8">
      <MainContentLayout>
        <PropertiesContent conversions={conversion} data={getHomepageListing.data?.popular || []} title={t('listing.popularProperty.title')} />
      </MainContentLayout>
      <CategoryContent />
      <MainContentLayout>
        <PropertiesContent conversions={conversion} data={getHomepageListing.data?.newest || []} title={t('listing.newestProperty.title')} forceLazyLoad />
      </MainContentLayout>
      <ClientHowItWorksContent />
      <BlogContent />
      <MainContentLayout>
        <PropertiesContent conversions={conversion} data={getHomepageListing.data?.featured || []} title={t('listing.featuredProperty.title')} forceLazyLoad />
      </MainContentLayout>

      <SeekerFaQContent />
    </div>
    <SeekerSeo />
    <ClientPage statusCode={status_code} accessToken={at} expiredAt={ma} />
  </>
  );
}

{/* <MainContentLayout>
        <PropertiesContent data={getHomepageListing.data?.commercial || []} title={t('listing.newestCommercialProperty.title')} />
      </MainContentLayout> */}
