"use client"

import * as React from "react"
import * as SliderPrimitive from "@radix-ui/react-slider"

import { cn } from "@/lib/utils"

const Slider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>
>(({ className, min, max, value, ...props }, ref) => {
  const initialValue = Array.isArray(value) ? value : [min, max]
  const [localValues, setLocalValues] = React.useState(initialValue)

  React.useEffect(() => {
    setLocalValues(Array.isArray(value) ? value : [min, max])
  }, [min, max, value])

  const handleValueChange = (newValues: number[]) => {
    setLocalValues(newValues);
    props.onValueChange?.(newValues)
  };

  return <SliderPrimitive.Root
    ref={ref}
    className={cn(
      "relative flex w-full touch-none select-none items-center",
      className
    )}
    onValueChange={handleValueChange}
    {...props}
  >
    <SliderPrimitive.Track className="relative h-1.5 w-full grow overflow-hidden rounded-full bg-seekers-text/20">
      <SliderPrimitive.Range className="absolute h-full bg-seekers-text" />
    </SliderPrimitive.Track>
    {localValues.map((value, index) => (
      <React.Fragment key={index} >
        <SliderPrimitive.Thumb id={"thumb" + (index + 1)} className="block h-4 w-4 rounded-full border border-primary/50 bg-background shadow transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50" />
      </React.Fragment>
    ))}
  </SliderPrimitive.Root>
})
Slider.displayName = SliderPrimitive.Root.displayName

export { Slider }
