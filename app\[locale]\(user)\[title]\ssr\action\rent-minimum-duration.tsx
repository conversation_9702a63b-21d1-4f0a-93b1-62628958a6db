import { formatDateSuffix, listingContract, ListingContractType } from "@/core/domain/listing/listing";
import { ItemWithSuffix } from "@/core/domain/utils/utils";
import { useTranslations } from "next-intl";

export default function RentMinimumDuration({ type, minDuration }: { type: ListingContractType, minDuration: ItemWithSuffix }) {
  const t = useTranslations("seeker")
  return <>
    {
      type == listingContract.rent &&
      <p className="text-seekers-text-light inline-flex justify-between md:w-full gap-2">
        <span className="flex gap-2 items-center">
          <span className="w-2 h-2 bg-seekers-text-lighter rounded-full"></span>
          {t('listing.misc.minimumRent')}
        </span>
        <span className="font-semibold">
          {minDuration.value} {
            formatDateSuffix(minDuration.suffix as string) == "YEAR" ? t("misc.yearWithCount", { count: minDuration.value }) : t("misc.month", { count: minDuration.value })}
        </span>
      </p>
    }
  </>
}