import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useTranslations } from 'next-intl'
import EmailDiscountPopup from '../email-discount-pop-up'
import { useUserStore } from '@/stores/user.store'
import { useJoinWaitingList } from '@/core/applications/mutations/waiting-list/use-join-waiting-list'

// Mock dependencies
jest.mock('next-intl')
jest.mock('@/stores/user.store')
jest.mock('@/core/applications/mutations/waiting-list/use-join-waiting-list')
jest.mock('framer-motion', () => ({
  useMotionValueEvent: jest.fn(),
  useScroll: () => ({ scrollYProgress: { get: () => 0.5 } })
}))

const mockUseTranslations = useTranslations as jest.MockedFunction<typeof useTranslations>
const mockUseUserStore = useUserStore as jest.MockedFunction<typeof useUserStore>
const mockUseJoinWaitingList = useJoinWaitingList as jest.MockedFunction<typeof useJoinWaitingList>

describe('EmailDiscountPopup', () => {
  const mockT = jest.fn((key: string, params?: any) => {
    const translations: Record<string, string> = {
      'promotion.popUp.title': '✨ Your Bali Property Journey Starts Here',
      'promotion.popUp.description': 'Get your exclusive 25% off code right away!',
      'promotion.popUp.couponCodeDescription': 'Use code WELCOME25 to save on your first subscription',
      'promotion.popUp.termsAndCondition': 'By using this offer, you agree to our Terms and Privacy.',
      'cta.getDiscount': 'Get discount code',
      'cta.useDiscountCode': 'Yes, I Want the Discount!',
      'misc.maybeLater': 'Maybe later',
      'form.label.email': 'Email'
    }
    return translations[key] || key
  })

  const mockMutateAsync = jest.fn()
  const mockToast = jest.fn()

  beforeEach(() => {
    mockUseTranslations.mockReturnValue(mockT)
    mockUseUserStore.mockReturnValue({
      seekers: { email: null },
      hydrated: true
    })
    mockUseJoinWaitingList.mockReturnValue({
      mutateAsync: mockMutateAsync,
      isPending: false
    })
    
    // Mock useToast
    jest.doMock('@/hooks/use-toast', () => ({
      useToast: () => ({ toast: mockToast })
    }))
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('should render enhanced popup with improved design', async () => {
    render(<EmailDiscountPopup />)
    
    // Should show enhanced header with visual elements
    expect(screen.getByText('🎉 LIMITED TIME OFFER')).toBeInTheDocument()
    expect(screen.getByText("Don't Get Scammed in Bali!")).toBeInTheDocument()
    expect(screen.getByText('Get 25% OFF + Exclusive Scam Protection Guide')).toBeInTheDocument()
  })

  it('should show social proof and trust indicators', async () => {
    render(<EmailDiscountPopup />)
    
    expect(screen.getByText('Join 2,500+ Smart Expats Who Avoided Bali Scams')).toBeInTheDocument()
    expect(screen.getByText('2,500+')).toBeInTheDocument()
    expect(screen.getByText('Protected Expats')).toBeInTheDocument()
    expect(screen.getByText('4.9/5')).toBeInTheDocument()
    expect(screen.getByText('Trust Rating')).toBeInTheDocument()
  })

  it('should show enhanced CTA button', async () => {
    render(<EmailDiscountPopup />)
    
    const ctaButton = screen.getByText('🎯 Get My 25% Discount + Scam Guide')
    expect(ctaButton).toBeInTheDocument()
    expect(ctaButton).toHaveClass('bg-gradient-to-r', 'from-teal-600', 'to-emerald-600')
  })

  it('should show trust indicators in form', async () => {
    render(<EmailDiscountPopup />)
    
    expect(screen.getByText('100% Secure')).toBeInTheDocument()
    expect(screen.getByText('Instant Access')).toBeInTheDocument()
    expect(screen.getByText('No Spam')).toBeInTheDocument()
  })

  it('should handle email submission with enhanced tracking', async () => {
    render(<EmailDiscountPopup />)
    
    const emailInput = screen.getByLabelText('Enter your email address')
    const submitButton = screen.getByText('🎯 Get My 25% Discount + Scam Guide')
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(mockMutateAsync).toHaveBeenCalledWith({
        email: '<EMAIL>',
        name: 'discount-popup-lead'
      })
    })
  })

  it('should show success state with enhanced design', async () => {
    mockMutateAsync.mockResolvedValueOnce({})
    
    render(<EmailDiscountPopup />)
    
    const emailInput = screen.getByLabelText('Enter your email address')
    const submitButton = screen.getByText('🎯 Get My 25% Discount + Scam Guide')
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText('🎉 Your Discount Code is Ready!')).toBeInTheDocument()
      expect(screen.getByText('WELCOME25')).toBeInTheDocument()
      expect(screen.getByText('🚀 Claim Your Discount Now')).toBeInTheDocument()
    })
  })

  it('should show urgency element after delay', async () => {
    jest.useFakeTimers()
    
    render(<EmailDiscountPopup />)
    
    // Fast forward time to trigger urgency
    jest.advanceTimersByTime(10000)
    
    await waitFor(() => {
      expect(screen.getByText('⚡ Only 47 discount codes left today!')).toBeInTheDocument()
    })
    
    jest.useRealTimers()
  })
})
