import { getBatchListingByIdService } from "@/core/infrastructures/listing/service";
import { useQuery } from "@tanstack/react-query";

export const BATCH_LISTING_QUERY_KEY = "batch-listings";
export function useGetBatchListing(codes: string[], isEnabled: boolean) {
  const query = useQuery({
    queryKey: [BATCH_LISTING_QUERY_KEY, ...codes],
    queryFn: async () => await getBatchListingByIdService(codes),
    enabled: isEnabled,
  });
  return query;
}
