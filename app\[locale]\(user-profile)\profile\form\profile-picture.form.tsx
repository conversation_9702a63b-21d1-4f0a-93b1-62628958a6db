"use client"
import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import CopyContent from "@/components/utility/copy-content";
import { useUploadFile } from "@/core/applications/mutations/file-upload/use-upload-file";
import { useUpadeUserDetail } from "@/core/applications/mutations/user/use-update-user-detail";
import { useGetMyDetail } from "@/core/applications/queries/users/use-get-me";
import { compressImage } from "@/core/domain/file-upload/file-upload";
import { UpdateUserDto } from "@/core/infrastructures/user/dto";
import { useToast } from "@/hooks/use-toast";
import { useUserStore } from "@/stores/user.store";
import { AvatarImage } from "@radix-ui/react-avatar";
import { User } from "lucide-react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { ChangeEvent, useRef, } from "react";

const IMAGE_UPLOAD_SIZE = 240
const IMAGE_SIZE = 96
export default function ProfilePictureForm() {
  const t = useTranslations("seeker")
  const { toast } = useToast()
  const { image, firstName, lastName } = useUserStore(state => state.seekers.accounts)
  const getMeQuery = useGetMyDetail()
  const useImageUpload = useUploadFile()
  const useUpdateUserMutation = useUpadeUserDetail()
  const handleImageUpload = async (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files == null || files?.length == 0) {
      toast({
        title: t('misc.noImageUploaded')
      })
      return
    }
    const imageCompressed = await compressImage(files[0], { width: IMAGE_UPLOAD_SIZE, height: IMAGE_UPLOAD_SIZE, resize: "cover" })
    if (imageCompressed == undefined) {
      return
    }
    const formData = new FormData()
    formData.append("file", imageCompressed as File)
    try {

      const result = await useImageUpload.mutateAsync(formData)
      if (result) {
        const url = result.data.data.url
        const data: UpdateUserDto = {
          image: url
        }
        useUpdateUserMutation.mutate(data)
      }
    } catch (e) {
      return
    }
  }
  const inputFileRef = useRef<HTMLInputElement | null>(null)
  return <div className="relative flex justify-start items-center gap-4">
    {
      getMeQuery.isLoading ?
        <Skeleton className="w-[96px] h-[96px] rounded-full" />
        :
        <Avatar className="w-[96px] h-[96px] relative border">
          {image ?
            <AvatarImage src={image} width={IMAGE_SIZE} height={IMAGE_SIZE} alt='example' className="rounded-full" />
            :
            <AvatarFallback>
              {firstName.charAt(0)}
              {lastName.charAt(0)}
            </AvatarFallback>
          }
          <input type="file" className="hidden" accept="image/*" ref={inputFileRef} onChange={handleImageUpload} />
        </Avatar>
    }
    <Button
      onClick={() => inputFileRef.current?.click()}
      variant={"outline"}
      loading={useImageUpload.isPending || useUpdateUserMutation.isPending}
      className="shadow-none"
    >
      {t('cta.changePhoto')}
    </Button>
  </div>
}