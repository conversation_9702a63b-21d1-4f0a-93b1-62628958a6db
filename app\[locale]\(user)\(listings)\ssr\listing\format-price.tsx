"use client"

import { formatCurrency } from "@/lib/utils"
import { useSeekersSettingsStore } from "@/stores/seekers-settings.store"
import { useLocale } from "next-intl"
import { useEffect, useState } from "react"

type CurrencyConversion = { [key: string]: number }
export default function FormatPrice({ price, currency_ = "EUR", conversion }: { price: number, currency_: string, locale_: string, conversion: CurrencyConversion }) {
  const { currency: currencyStored, isLoading } = useSeekersSettingsStore()
  const [currency, setCurrency] = useState(currency_)
  const locale = useLocale()
  useEffect(() => {
    if (isLoading) return
    setCurrency(currencyStored)
  }, [currencyStored, isLoading])
  return formatCurrency((price * (conversion[currency] || 1)) || 0, currency, locale)

}