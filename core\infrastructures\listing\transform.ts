import {
  FilterParameter,
  ListingImageSeekers,
  ListingListSeekers,
  ListingSeekerDetail,
} from "@/core/domain/listing/listing-seekers";
import {
  GetFilterParameterDto,
  ListingDetailDto,
  ListingListDto,
  ListingSeekerDetailDto,
  ListingSeekersDto,
  ListingSeekersImageDto,
  LocationSuggestionDto,
} from "./dto";
import {
  formatBedroomSuffix,
  formatCleaningSuffix,
  formatDateSuffix,
  formatWifiSuffix,
  ListingDetail,
  ListingImage,
  ListingList,
  ListingSitemap,
  ListingType,
} from "@/core/domain/listing/listing";
import _ from "lodash";
import leven from "leven";

export function transfromListings(dto: ListingListDto[]): ListingList[] {
  const imageIndex = 0;
  return dto.map((item) => ({
    accountId: item.account_id,
    code: item.code,
    geolocation: item.geolocation,
    location: item.location,
    status: item.status as ListingType,
    price: item.price,
    expiryDate: item.adjusted_expiry_date || item.expiry_date || undefined,
    thumbnail: [
      {
        id: item.thumbnail[imageIndex].id,
        image: item.thumbnail[imageIndex].image,
        isHighlight: item.thumbnail[imageIndex].is_highlight,
        order: item.thumbnail[imageIndex].order,
        propertyId: item.thumbnail[imageIndex].property_id,
      },
    ],
    title: item.title,
  }));
}
export function transformDetailListing(dto: ListingDetailDto): ListingDetail {
  const availability = dto.availability;
  const detail = dto.detail;
  const features = dto.features;
  const location = dto.location;
  const images: ListingImage[] = dto.images.map((item) => ({
    id: item.id,
    image: item.image,
    isHighlight: item.is_highlight,
    order: item.order,
    propertyId: item.property_id,
  }));
  const highlightedImage = images.find(
    (item) => item.isHighlight
  ) as ListingImage;
  highlightedImage.order = 1;
  const nonHighlightedImage = images
    .filter((item) => !item.isHighlight)
    .map((item, idx) => ({ ...item, order: idx + 2 })); // all nonhighlighted image should start at order 2
  const finalImage: ListingImage[] = [highlightedImage, ...nonHighlightedImage];
  return {
    availability: {
      availableAt: availability.available_at,
      isNegotiable: availability.is_negotiable,
      maxDuration: +availability.duration || 0,
      minDuration: +availability.duration || 0,
      price: availability.price,
      type: availability.type?.value || "",
      typeMaximumDuration: formatDateSuffix(
        availability.duration_unit?.suffix || ""
      ),
      typeMinimumDuration: formatDateSuffix(
        availability.duration_unit?.suffix || ""
      ),
    },
    description: dto.description,
    detail: {
      bathroomTotal: +detail.bathroom_total?.value || 0,
      bedroomTotal: +detail.bedroom_total?.value || 0,
      buildingSize: +detail.building_size || 0,
      cascoStatus: detail.casco_status,
      cleaningService: +detail.cleaning_service?.value || 0,
      garbageFee: detail.garbage_fee,
      gardenSize: +detail.garden_size || 0,
      landSize: +detail.land_size || 0,
      propertyOfView: detail.property_of_view,
      type: detail.option.type,
      villageFee: detail.village_fee,
      waterFee: detail.water_fee,
      wifiService: +detail.wifi_service?.value || 0,
      typeWifiSpeed: formatWifiSuffix(detail.wifi_service.suffix || ""),
      yearsOfBuilding: detail.years_of_building,
      typeBedRoom: formatBedroomSuffix(detail.bedroom_total.suffix || ""),
      typeCleaning: formatCleaningSuffix(detail.cleaning_service.suffix || ""),
      title: dto.title,
      excerpt: dto.excerpt,
    },
    excerpt: dto.excerpt,
    features: {
      amenities: (features.amenities || []).map((item) => item.value),
      electricity: +features.electricity,
      furnishingOption: features.furnishing_option?.value as any,
      livingOption: (features.living_option?.value || "") as string,
      parkingOption: (features.parking_option?.value || "") as any,
      poolOption: (features.pool_option?.value || "") as any,
      sellingPoints: (features.selling_points || []).map((item) => item.value),
    },
    id: dto.id,
    images: finalImage,
    location: {
      city: location.city,
      district: location.district,
      latitude: location.latitude,
      longitude: location.longitude,
      mainAddress: location.main_address,
      postalCode: location.postal_code,
      province: location.province,
      roadSize: +(location.road_size?.value || 0),
      secondAddress: location.second_address,
      type: location.type.value,
      banjar: location.additional_address?.title || "",
    },
    propertyId: detail.property_id,
    status: dto.status as ListingType,
    title: dto.title,
  };
}
export function transformDetailSeekersListing(
  dto: ListingSeekerDetailDto
): ListingSeekerDetail {
  const availability = dto.availability;
  const detail = dto.detail;
  const features = dto.features;
  const location = dto.location;
  const [lat, lng] = addRandomOffset(location.latitude, location.longitude);
  const images: ListingImage[] = dto.images.map((item) => ({
    id: item.id,
    image: item.image,
    isHighlight: item.is_highlight,
    order: item.order,
    propertyId: item.property_id,
  }));
  const highlightedImage = images.find(
    (item) => item.isHighlight
  ) as ListingImage;
  highlightedImage.order = 1;
  const nonHighlightedImage = images
    .filter((item) => !item.isHighlight)
    .map((item, idx) => ({ ...item, order: idx + 2 })); // all non-highlighted image should start at order 2
  const finalImage: ListingImage[] = [highlightedImage, ...nonHighlightedImage];
  return {
    availability: {
      availableAt: availability.available_at,
      isNegotiable: availability.is_negotiable,
      maxDuration: availability?.duration_max || 0,
      minDuration: availability?.duration_min || 0,
      price: availability.price,
      type: availability.type?.value || "",
      typeMaximumDuration: formatDateSuffix(
        availability.duration_max_unit?.value || ""
      ),
      typeMinimumDuration: formatDateSuffix(
        availability.duration_min_unit?.value || ""
      ),
    },
    description: dto.description,
    detail: {
      bathroomTotal: +detail.bathroom_total?.value || 0,
      bedroomTotal: +detail.bedroom_total?.value || 0,
      buildingSize: +detail.building_size || 0,
      cascoStatus: detail.casco_status,
      cleaningService: +detail.cleaning_service?.value || 0,
      garbageFee: detail.garbage_fee,
      gardenSize: +detail.garden_size || 0,
      landSize: +detail.land_size || 0,
      propertyOfView: detail.property_of_view,
      type: detail.option.type,
      villageFee: detail.village_fee,
      waterFee: detail.water_fee,
      wifiService: +detail.wifi_service?.value || 0,
      typeWifiSpeed: formatWifiSuffix(detail.wifi_service.suffix || ""),
      yearsOfBuilding: detail.years_of_building,
      typeBedRoom: formatBedroomSuffix(detail.bedroom_total.suffix || ""),
      typeCleaning: formatCleaningSuffix(detail.cleaning_service.suffix || ""),
      title: dto.title,
      excerpt: dto.excerpt,
    },
    excerpt: dto.excerpt,
    features: {
      amenities: (features.amenities || []).map((item) => item.value),
      electricity: +features.electricity,
      furnishingOption: features.furnishing_option?.value as any,
      livingOption: (features.living_option?.value || "") as string,
      parkingOption: (features.parking_option?.value || "") as any,
      poolOption: (features.pool_option?.value || "") as any,
      sellingPoints: (features.selling_points || []).map((item) => item.value),
    },
    id: dto.id,
    images: finalImage,
    location: {
      city: location.city,
      district: location.district,
      latitude: lat,
      longitude: lng,
      mainAddress: location.main_address,
      postalCode: location.postal_code,
      province: location.province,
      roadSize: +(location.road_size?.value || 0),
      secondAddress: location.second_address,
      type: location.type.value,
      banjar: "",
    },
    propertyId: detail.property_id,
    status: dto.status as ListingType,
    title: dto.title,
    owner: dto.owner
      ? {
          name: dto.owner.full_name,
          image: dto.owner.image,
          code: dto.owner.user.id,
        }
      : null,
    middleman: dto.middleman
      ? {
          code: dto.middleman.user.id,
          image: dto.middleman.image || "",
          name: dto.middleman.full_name,
        }
      : null,
    isFavorite: +(dto?._count?.favorites || 0) > 0 ? true : false,
    chatCount: dto.account?.user._count.chats || 0,
  };
}

function transformSeekersImage(
  code: string,
  images: ListingSeekersImageDto[]
): ListingImageSeekers[] {
  const data = images.map((img, idx) => ({
    id: code + idx,
    image: img.image,
    isHighlight: img.is_highlight,
  })) as ListingImageSeekers[];
  const sorted = data.sort((a, b) => +b.isHighlight - +a.isHighlight);
  return sorted;
}
export function transformSeekersListing(
  dto: ListingSeekersDto[]
): ListingListSeekers[] {
  const transformedDto: ListingListSeekers[] = dto.map((item) => ({
    code: item.code,
    geolocation: addRandomOffset(
      item.location.latitude,
      item.location.longitude
    ),
    location:
      item.location.district +
      ", " +
      item.location.city +
      ", " +
      item.location.province,
    price: item.availability.price,
    thumbnail: transformSeekersImage(item.code, item.images),
    title: item.title,
    listingDetail: {
      bathRoom: item.detail.bathroom_total,
      bedRoom: item.detail.bedroom_total,
      buildingSize: item.detail.building_size,
      landSize: item.detail.land_size,
      cascoStatus: item.detail.casco_status,
      gardenSize: item.detail.garden_size,
    },
    availability: {
      availableAt: item.availability.available_at || "",
      maxDuration:
        item.availability.duration_max_unit?.value &&
        item.availability.duration_max
          ? {
              value: item.availability.duration_max || 1,
              suffix: item.availability.duration_max_unit?.value,
            }
          : null,
      minDuration:
        item.availability.duration_min_unit?.value &&
        item.availability.duration_min
          ? {
              value: item.availability.duration_min || 1,
              suffix: item.availability.duration_min_unit?.value,
            }
          : null,
      type: item.availability.type.value || "",
    },
    sellingPoint: item.features.selling_points,
    category: item.detail.option.type,
    isFavorite: item?._count?.favorites > 0 ? true : false,
    status: item.status,
  }));
  return transformedDto;
}

export function transformLocationSuggestionListing(
  search: string,
  dto: LocationSuggestionDto[]
): string[] {
  const suggestion: string[] = [];
  dto.forEach((item) => {
    Object.values(item).forEach((val: string) => {
      const similarity = calculateSimilarity(val, search);
      if (similarity > 0) {
        suggestion.push(val);
      }
    });
  });
  const suggestionClean = _.uniq(suggestion);
  return suggestionClean;
}

export function transfromSeekersFilterParameter(
  dto: GetFilterParameterDto
): FilterParameter {
  return {
    priceRange: {
      min: dto.price_range._min.price,
      max: dto.price_range._max.price,
    },
    buildingSizeRange: {
      max: dto.size_range._max.building_size,
      min: dto.size_range._min.building_size,
    },
    gardenSizeRange: {
      max: dto.size_range._max.garden_size,
      min: dto.size_range._min.garden_size,
    },
    landSizeRange: {
      max: dto.size_range._max.land_size,
      min: dto.size_range._min.land_size,
    },
    furnishingOptions: dto.furnishing_options[0].childrens.map((item) => ({
      title: item.title,
      value: item.value,
    })),
    livingOptions: dto.living_options[0].childrens.map((item) => ({
      title: item.title,
      value: item.value,
    })),
    parkingOptions: dto.parking_options[0].childrens.map((item) => ({
      title: item.title,
      value: item.value,
    })),
    poolOptions: dto.pool_options[0].childrens.map((item) => ({
      title: item.title,
      value: item.value,
    })),
  };
}

export function transformPropertiesSitemap(
  dto: ListingSeekersDto[]
): ListingSitemap[] {
  return dto.map((item) => ({
    title: item.title.replaceAll(/[^a-zA-Z0-9]/g, "-"),
    id: item.code,
    updateAt: item.availability.updated_at,
  }));
}

function calculateSimilarity(value: string, search: string) {
  const distance = leven(value.toLowerCase(), search.toLowerCase());
  const maxLength = Math.max(value.length, search.length);
  return 1 - distance / maxLength;
}

const addRandomOffset = (
  latitude: number,
  longitude: number,
  maxOffsetMeters = 10
): [number, number] => {
  const metersToDegrees = 1 / 111320; // Conversion factor: ~1 meter in degrees
  const offset = maxOffsetMeters * metersToDegrees;

  const randomOffsetLat = 0.2 * 2 * offset; // Random value in range [-offset, offset]
  const randomOffsetLng = 0.2 * 2 * offset;
  return [latitude + randomOffsetLat, longitude + randomOffsetLng];
};
