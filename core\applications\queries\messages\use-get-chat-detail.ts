import { getChatRoomDetailService } from "@/core/infrastructures/messages/services";
import { useQuery } from "@tanstack/react-query";

export const CHAT_DETAIL = "chat-detail";
export default function useGetChatDetail(id: string, isEnable: boolean = true) {
  const query = useQuery({
    queryKey: [CHAT_DETAIL, id],
    queryFn: () => getChatRoomDetailService(id),
    retry: 0,
    enabled: isEnable,
  });
  return query;
}
