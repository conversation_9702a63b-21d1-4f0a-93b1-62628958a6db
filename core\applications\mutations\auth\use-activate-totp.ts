import { postTwoFactorAuthenticationCode } from "@/core/infrastructures/auth";
import {
  TotpVerificationCode,
  TwoFactorAuthenticationCodeDto,
} from "@/core/infrastructures/auth/dto";
import { useMutation } from "@tanstack/react-query";

// TOPT = Two Factor Authtentication
export function useActivateTotp() {
  const mutation = useMutation({
    mutationFn: (data: TwoFactorAuthenticationCodeDto) =>
      postTwoFactorAuthenticationCode({
        ...data,
        request_setting: "ACTIVE_2FA",
      }),
  });
  return mutation;
}
