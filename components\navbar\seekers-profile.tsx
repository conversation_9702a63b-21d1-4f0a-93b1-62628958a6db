"use client"

import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar"
import { User } from "lucide-react"
import { useUserStore } from "@/stores/user.store"
import { useGetMyDetail } from "@/core/applications/queries/users/use-get-me"
import { cn } from "@/lib/utils"
import { useTranslations } from "next-intl"

export default function SeekersProfile({ url, className }: { url: string, className?: string }) {
  useGetMyDetail()
  const { seekers } = useUserStore()
  const t = useTranslations("universal")
  return <>
    <Avatar className={cn("w-full rounded-full bg-seekers-text-lighter flex justify-center items-center", className)}>
      <AvatarImage src={seekers.accounts?.image} alt={t('misc.profileImageAlt')} />
      <AvatarFallback className="bg-transparent text-white">
        {seekers.code ? <span>{seekers.accounts.firstName[0]}{seekers.accounts.lastName[0]}</span> :
          <User />
        }
      </AvatarFallback>
    </Avatar>
  </>
}