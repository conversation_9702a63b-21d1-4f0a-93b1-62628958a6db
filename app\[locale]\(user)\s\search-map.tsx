"use client"
import { useSeekerFilterResultStore } from "@/stores/seeker-filter-result.store";
import { Map, useMap, } from "@vis.gl/react-google-maps";
import { useEffect, useState } from "react";
import PinMapListings from "./pin-map-listing";
import { ClusteredPinMapListing } from "./clustered-maps/clustered-pin-map-listing";
import { useSeekersSearchMapUtil } from "@/stores/seekers-search-map-utils";
import { useDebounce } from "@/hooks/use-debounce";
import useSearchParamWrapper from "@/hooks/use-search-param-wrapper";
import SubscribeBanner from "@/components/subscribe/subscribe-map-banner";
import { useToast } from "@/hooks/use-toast";
import { filterTitles } from "@/lib/constanta/constant";
import { useUserStore } from "@/stores/user.store";
import { packages } from "@/core/domain/subscription/subscription";
// TODO: will implement this when there's alreqady subscription for user


interface Coordinate {
  lat: string,
  lng: string
}
export default function SearchMap({ conversions }: { lat: number, lng: number, conversions: { [key: string]: number } }) {
  const { createMultipleQueryString, searchParams } = useSearchParamWrapper()
  const { data } = useSeekerFilterResultStore()
  const { seekers } = useUserStore()
  const maps = useMap()
  const { setFocusedListing } = useSeekersSearchMapUtil()
  const [boundMap, setBoundMap] = useState<[number, number, number] | null>()
  const debounce = useDebounce(boundMap)

  const [showBanner, setShowBanner] = useState(false)
  const [zoomLevel, setZoomLevel] = useState(12)
  const { toast } = useToast()
  // useEffect to init center of the map when first time fetch data 
  useEffect(() => {
    if (data.length > 1) {
      const firstData = data[0]
      maps?.moveCamera({ center: { lat: firstData.geolocation[0], lng: firstData.geolocation[1] } })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data])

  useEffect(() => {
    if (debounce == null) return
    if (searchParams.get(filterTitles.viewMode) == "list" || searchParams.get(filterTitles.viewMode) == null) return
    const lat = {
      name: "lat",
      value: debounce[0].toString()
    }
    const lng = {
      name: "lng",
      value: debounce[1].toString()
    }
    const zoom = {
      name: filterTitles.zoom,
      value: debounce[2].toString()
    }
    createMultipleQueryString([lat, lng, zoom])
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounce])

  return <div className="rounded-lg overflow-hidden relative w-full h-full">
    {showBanner &&
      <>
        <SubscribeBanner />
      </>
    }

    <Map
      reuseMaps

      mapId={process.env.NEXT_PUBLIC_GOOGLE_MAPS_ID}
      style={{ width: "100%", height: "100%" }}
      mapTypeControl={false}
      fullscreenControl={false}
      defaultZoom={12}
      defaultCenter={{ lat: -8.639736, lng: 115.1341357 }}
      maxZoom={seekers.accounts.zoomFeature.max}
      minZoom={seekers.accounts.zoomFeature.min}
      disableDefaultUI
      onDragend={e => {
        if (e.map.getCenter()) {
          setBoundMap([e.map.getCenter()?.lat()!, e.map.getCenter()?.lng()!, e.map.getZoom()!])
          setFocusedListing(null)
        }

      }}
      onZoomChanged={e => {
        if (e.detail.zoom >= seekers.accounts.zoomFeature.max
          && zoomLevel !== e.detail.zoom
          && seekers.accounts.membership === packages.free
        ) {
          setShowBanner(true)

        } else {
          setShowBanner(false)
        }
        setBoundMap([e.map.getCenter()?.lat()!, e.map.getCenter()?.lng()!, e.map.getZoom()!])
        setZoomLevel(e.map.getZoom()!)
        setFocusedListing(null)


      }}
    >
      {(searchParams.get(filterTitles.viewMode) == "list" || searchParams.get(filterTitles.viewMode) == null) ?
        data.map(item => <PinMapListings
          conversions={conversions}
          key={item.code}
          data={item}
        />) :
        <ClusteredPinMapListing
          conversions={conversions}
          data={data} />
      }
    </Map>
  </div >
}