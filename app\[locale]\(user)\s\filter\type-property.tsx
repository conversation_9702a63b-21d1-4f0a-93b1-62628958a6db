"use client"

import { BaseSelectInputValue } from "@/types/base"
import FilterContentLayout from "./filter-content-layout"
import { cn } from "@/lib/utils"
import { useTranslations } from "next-intl"
import { SeekerListingFilterType, seekersListingFilterType } from "@/core/domain/listing/listing-seekers"
import { useSeekerFilterStore } from "@/stores/seekers-filter-2.store"

export default function TypeProperty() {
  const t = useTranslations("seeker")
  const typeProperty = useSeekerFilterStore(state => state.typeProperty)
  const setTypeProperty = useSeekerFilterStore(state => state.setTypeProperty)
  const propertyType: BaseSelectInputValue<SeekerListingFilterType>[] = [
    {
      id: "1",
      content: <span className=""> {t('listing.filter.typeProperty.optionOne.title')}</span>,
      value: seekersListingFilterType.anything
    },
    {
      id: "2",
      content: <span className="">{t('listing.filter.typeProperty.optionTwo.title')}</span>,
      value: seekersListingFilterType.placeToLive
    },
    {
      id: "3",
      content: <span className="">{t('listing.filter.typeProperty.optionThree.title')}</span>,
      value: seekersListingFilterType.business
    },
    {
      id: "4",
      content: <span className="">{t('listing.filter.typeProperty.optionFour.title')}</span>,
      value: seekersListingFilterType.land
    },
  ]
  return <FilterContentLayout title={t('listing.filter.typeProperty.title')} description={t('listing.filter.typeProperty.description')}>
    <div className="w-full grid grid-cols-2 gap-0.5 lg:grid-cols-4 border-2 rounded-xl border-[#F0F0F0] overflow-hidden">
      {propertyType.map(item => <div
        key={item.id}
        onClick={() => setTypeProperty(item.value)}
        className={
          cn("px-4 h-10 hover:bg-accent flex justify-center items-center cursor-pointer font-medium text-xs",
            (typeProperty == item.value) ? "bg-seekers-primary text-white hover:bg-seekers-primary-light" : "text-seekers-text")}>
        {item.content}
      </div>)}
    </div>
  </FilterContentLayout>
}