import { BaseInputForm, BaseSelectInputValue } from "@/types/base"
import { ReactNode } from "react"
import { FieldValues } from "react-hook-form"
import BaseInputLayout from "./base-input"
import { Input } from "../ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select"
import { FormControl, FormField, } from '@/components/ui/form'
import { Label } from "../ui/label"

interface InputWithSelectProps<T extends FieldValues> extends Pick<BaseInputForm<T>, "form"> {
  label?: string,
  input: {
    type: string
    placeholder: string
  } & Pick<BaseInputForm<T>, "name">
  select: {
    selectList?: BaseSelectInputValue<string>[]
    placeholder: string
    children?: ReactNode
  } & Pick<BaseInputForm<T>, "name">
  disabled?: boolean

}
export default function InputWithSelect<T extends FieldValues>({ input, label, select, form, disabled }: InputWithSelectProps<T>) {
  return <div className="space-y-2">
    <Label>{label}</Label>
    <div className="flex w-full border rounded-sm">
      <div className="flex-grow border-r">
        <FormField
          control={form.control}
          name={input.name}
          render={({ field }) => (
            <BaseInputLayout>
              <Input
                placeholder={input.placeholder} disabled={field.disabled || disabled} {...field}
                className='border-none focus:outline-none flex-grow shadow-none focus-visible:ring-0 rounded-r-none' />
            </BaseInputLayout>
          )}
        />
      </div>
      <div className="min-w-[25%]">
        <FormField
          control={form.control}
          name={select.name}
          render={({ field }) => (
            <Select onValueChange={field.onChange} defaultValue={field.value} disabled={field.disabled || disabled} >
              <FormControl>
                <SelectTrigger className=" border-none shadow-none focus:outline-none flex-grow focus-visible:ring-0">
                  <SelectValue placeholder={select.placeholder} />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {
                  Array.isArray(select.selectList) &&
                  select.selectList.map(item => <SelectItem key={item.id} value={item.value}>
                    {item.content}
                  </SelectItem>
                  )
                }
                {select.children}
              </SelectContent>
            </Select>
          )}
        />
      </div>
    </div>
  </div >
}