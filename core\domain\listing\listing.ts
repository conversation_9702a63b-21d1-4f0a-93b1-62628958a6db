export const listingStatus = {
  approved: "APPROVED",
  online: "ONLINE",
  offline: "OFFLINE",
  rejected: "REJECTED",
  inactive: "DEACTIVATED",
  reactivate: "REACTIVATION",
  draft: "DRAFT",
  readyToApproval: "READY_FOR_APPROVAL",
  insufficientBalance: "INSUFFICIENT_BALANCE",
  expired: "EXPIRED",
} as const;
export type ListingType = (typeof listingStatus)[keyof typeof listingStatus];

type FurnishingOption = "FURNISHED" | "UNFURNISHED";

type ParkingOption = "PRIVATE" | "PUBLIC" | "NOT_AVAILABLE";

type PoolOption = "AVAILABLE" | "NOT_AVAILABLE";
export interface ListingImage {
  id: string;
  propertyId: string;
  image: string;
  order: number;
  isHighlight: boolean;
}
export interface ListingList {
  accountId: string;
  code: string;
  title: string;
  location: string;
  geolocation: string;
  status: ListingType;
  price: number;
  thumbnail: ListingImage[];
  expiryDate?: string;
}

interface ItemWithSuffix {
  value: number | string;
  suffix: string;
}
export interface BasicInformation {
  type: string;
  propertyOfView: string;
  yearsOfBuilding: number;
  landSize: number;
  buildingSize: number;
  gardenSize: number;
  bedroomTotal: number;
  bathroomTotal: number;
  wifiService: number;
  cleaningService: number;
  waterFee: boolean;
  garbageFee: boolean;
  villageFee: boolean;
  cascoStatus: boolean;
  typeBedRoom: string;
  typeCleaning: string;
  typeWifiSpeed: string;
  title: string;
  excerpt: string;
}
export interface Location {
  province: string;
  city: string;
  district: string;
  postalCode: string;
  mainAddress: string;
  secondAddress: string | null;
  type: string;
  roadSize: number;
  latitude: number;
  longitude: number;
  banjar: string;
}
export interface Availability {
  type: string;
  price: number;
  isNegotiable: boolean;
  availableAt: string;
  minDuration: number;
  maxDuration: number;
  typeMaximumDuration: string;
  typeMinimumDuration: string;
}

export interface Feature {
  electricity: number;
  amenities: string[];
  sellingPoints: string[];
  furnishingOption: FurnishingOption;
  parkingOption: ParkingOption;
  livingOption: string;
  poolOption: PoolOption;
}

export interface ListingDetail {
  id: string;
  propertyId: string;
  title: string;
  excerpt: string;
  description: string;
  detail: BasicInformation;
  location: Location;
  availability: Availability;
  features: Feature;
  images: ListingImage[];
  status: ListingType;
}

export interface ListingSitemap {
  title: string;
  id: string;
  updateAt: string;
}

export const hasAmenities = (amenities: string, amenitiesList?: string[]) =>
  !amenitiesList ? false : amenitiesList.includes(amenities);

export type DateType = "DAY" | "WEEK" | "MONTH" | "YEAR";
export const formatDateSuffix = (value: string): DateType => {
  if (value.toLowerCase().includes("day")) return "DAY";
  if (value.toLowerCase().includes("week")) return "WEEK";
  if (value.toLowerCase().includes("month")) return "MONTH";
  if (value.toLowerCase().includes("year")) return "YEAR";
  return "MONTH";
};

export type SizeType = "METER" | "METER_SQUARE" | "METER_QUBIC";
export const formatsizeSuffix = (value: string): SizeType => {
  if (value == "m") return "METER";
  if (value.includes("m2")) return "METER_SQUARE";
  if (value.includes("m3")) return "METER_QUBIC";
  return "METER";
};

export type WifiType = "MBPS" | "GBPS" | "KBPS";
export const formatWifiSuffix = (value: string): WifiType => {
  if (value.includes("kbps")) return "KBPS";
  if (value.includes("mbps")) return "MBPS";
  if (value.includes("gbps")) return "GBPS";
  return "MBPS";
};

export type CleaningType = "DAY" | "WEEK" | "MONTH";
export const formatCleaningSuffix = (value: string): CleaningType => {
  if (value.includes("day")) return "DAY";
  if (value.includes("week")) return "WEEK";
  if (value.includes("month")) return "MONTH";
  return "DAY";
};

export type BedroomType = "SEPERATED_ROOM" | "CONNECTED_ROOM";

export const formatBedroomSuffix = (value: string): BedroomType => {
  if (value.includes("Seperated Room")) return "SEPERATED_ROOM";
  return "CONNECTED_ROOM";
};

export const listingContract = {
  leasehold: "LEASEHOLD",
  freehold: "FREEHOLD",
  rent: "RENT",
} as const;
export type ListingContractType =
  (typeof listingContract)[keyof typeof listingContract];

export const isListingActive = (status: string) => status == "ONLINE";
