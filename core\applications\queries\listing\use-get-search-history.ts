import { getSearchHistoryService } from "@/core/infrastructures/listing/service";
import { useQuery } from "@tanstack/react-query";

export const SEARCH_HISTORY_QUERY_KEY = "search-history";
export function useGetSearchHistory(enabled?: boolean) {
  const query = useQuery({
    queryKey: [SEARCH_HISTORY_QUERY_KEY],
    queryFn: async () => await getSearchHistoryService(),
    enabled: enabled,
  });
  return query;
}
