"use client"
import { But<PERSON> } from "@/components/ui/button";
import { Carousel, CarouselContent, CarouselDots, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { imagePlaceholder } from "@/lib/constanta/image-placeholder";
import { X } from "lucide-react";
import Image from "next/image";
import { useEffect } from "react";

export default function ImageDetailCarousel({ imagesUrl, open, setOpen, selectedImageIndex }: {
  imagesUrl: string[], open: boolean, setOpen: (val: boolean) => void, selectedImageIndex: number
}) {

  useEffect(() => {
    const container = document.getElementsByTagName("body")[0]
    if (open) {
      container.style.overflow = "hidden"
    } else {
      container.style.overflow = "auto"
    }
  }, [open])
  return !open
    ? <></>
    : <div id="image-carousel-container" className="!mt-0 fixed  w-screen h-screen top-0 left-0 bg-black z-[60] flex flex-col justify-center isolate items-center">
      <Button variant={"ghost"} size={"icon"} className="text-white absolute top-4 left-4 z-[60]" onClick={() => setOpen(false)}>
        <X />
      </Button>
      <Carousel
        opts={{ loop: true, startIndex: selectedImageIndex }}
        className={"group isolate w-full h-full  relative  overflow-hidden"}

      >
        <CarouselContent className="absolute top-0 left-0 w-full h-full ml-0 -z-20">
          {imagesUrl.map((item, idx) => <CarouselItem key={idx} className="relative">
            <Image
              src={item}
              alt=""
              fill
              sizes="300px"
              loading="lazy"
              priority
              blurDataURL={imagePlaceholder}
              placeholder="blur"
              style={{
                objectFit: "contain"
              }}
            />
          </CarouselItem>
          )}
        </CarouselContent>

        {imagesUrl.length <= 1 ? <></> :
          <div className="flex absolute top-1/2 -translate-y-1/2 left-0 w-full justify-between px-3">
            <CarouselPrevious className="left-3 group-hover:opacity-100 opacity-0 transition duration-75 ease-in" />
            <CarouselNext className="right-3 group-hover:opacity-100 opacity-0 transition duration-75 ease-in" />
          </div>
        }
        <div className="flex absolute bottom-4 left-0 w-full items-center justify-center">
          <CarouselDots />
        </div>
      </Carousel>
    </div>
}