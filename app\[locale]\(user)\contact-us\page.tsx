import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import ContactUsContent from './contact-us-content';
import { contactUsUrl } from '@/lib/constanta/route';

export async function generateMetadata({ params: { locale } }: { params: { locale: string } }): Promise<Metadata> {
  const t = await getTranslations({ locale, namespace: 'ContactUs' });

  return {
    title: t('pageTitle'),
    description: t('pageDescription'),
    alternates: {
      languages: {
        en: process.env.USER_DOMAIN + "/en" + contactUsUrl,
        id: process.env.USER_DOMAIN + "/id" + contactUsUrl
      }
    }
  };
}

export default function ContactUsPage() {
  return <>
    {/* <TemporaryBlock /> */}
    <ContactUsContent />;
  </>
} 
