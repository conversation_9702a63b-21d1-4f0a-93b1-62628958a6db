import { create } from "zustand"

export interface Coordinate {
  lat:number,lng:number
}
export interface HighlightedListing {
  code: string,
  location: Coordinate
}

export type ViewMode = "map" | "list"
interface SeekersSearchMapUtil {
  viewMode: ViewMode,
  setViewMode: (viewMode: ViewMode) => void
  focusedListing: string | null,
  setFocusedListing: (val:string | null) => void, 
  highlightedListing:string | null,
  setHighlightedListing: (data:string| null) => void,
  zoom: number,
  setZoom: (zoom:number) => void,
  mapVariantId: number,
  setMapVariantId: (mapVariantId:number) => void
}

export const useSeekersSearchMapUtil = create<SeekersSearchMapUtil>()(set => ({
  focusedListing: null,
  highlightedListing: null,
  zoom: 13,
  mapVariantId: 0,
  viewMode: "list",
  setViewMode: (viewMode) => set(() => ({viewMode})),
  setMapVariantId: (mapVariantId:number) => set(() => ({mapVariantId})),
  setZoom: (zoom:number) => set(() =>   ({zoom})),
  setFocusedListing: (focusedListing:string | null) => set(() => ({focusedListing})),
  setHighlightedListing: (highlightedListing:string | null) => set(() => ({highlightedListing}))
}))