"use client"
import { usePathname } from "next/navigation"
import { useLocale, useTranslations } from "next-intl"
import { NavigationMenu } from "@/types/layout"
import { checkIfSameUrl } from "@/lib/utils"
import usePreventingExitPage from "@/hooks/use-preventing-exit-page"
import { useRouter } from 'nextjs-toploader/app';
export default function NavigationItem({ item }: { item: NavigationMenu }) {
  const route = usePathname()
  const router = useRouter()
  const locale = useLocale()
  const activeUrl = checkIfSameUrl(route, item.link)
  const t = useTranslations()
  const { handlePreventingExitPage } = usePreventingExitPage()
  const activeStyle = activeUrl ? "text-neutral-darkest" : "text-neutral-light"
  return <div onClick={() => {
    const continueAction = handlePreventingExitPage()
    if (continueAction) {
      router.push('/' + locale + item.link)
    }
  }} key={item.id} className={`cursor-pointer flex flex-col items-center gap-2 w-16 ${activeStyle} rounded-md hover:bg-foreground/50 p-2`}>
    {item.icon}
    <p className="text-[10px] text-center max-sm:hidden leading-tight ">{t(item.localeKey)}</p>
  </div>
}