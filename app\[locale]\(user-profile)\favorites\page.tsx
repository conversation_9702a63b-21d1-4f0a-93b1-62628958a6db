import FavoriteBreadCrumb from "./bread-crumb";
import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import FavoriteListingContent from "./content";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import FilterHeader from "../../(user)/s/filter-header";
import { BaseProps } from "@/types/base";
import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api";
import { filterTitles } from "@/lib/constanta/constant";
import { favoriteUrl } from "@/lib/constanta/route";


export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("seeker")
  return {
    title: t("metadata.favorite.title"),
    description: t("metadata.favorite.description"),
    alternates: {
      languages: {
        en: process.env.USER_DOMAIN + "/en" + favoriteUrl,
        id: process.env.USER_DOMAIN + "/id" + favoriteUrl
      }
    }

  }
}




export default async function FavoritePage({ params, searchParams }: BaseProps) {
  const t = await getTranslations("seeker")
  const conversionRates = await getCurrencyConversion()
  return <>
    <FavoriteBreadCrumb />
    <MainContentLayout className="space-y-8 my-8 max-sm:px-0">
      <div className="flex max-sm:flex-col justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">{t("setting.favorites.savedItems.title")}</h1>
          <h2 className="text-muted-foreground mt-2">{t("setting.favorites.savedItems.description")}</h2>
        </div>
        <FilterHeader showFilter={false} conversions={conversionRates.data || []} />
      </div>
      <FavoriteListingContent
        page={searchParams.page as string}
        types="all"
        query="all"
        sortBy={searchParams.sortBy as string || undefined}
        conversions={conversionRates.data}
      />
    </MainContentLayout>

  </>
}