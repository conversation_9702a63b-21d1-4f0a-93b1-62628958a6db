"use client"
import { useEffect, useRef, useState } from "react";
import { Input } from "../ui/input";
import { BaseItemList } from "@/types/base";
import { useTranslations } from "next-intl";


interface SearchWithOptionResultProps {
  onChange: (value: string) => void,
  listResults: BaseItemList[],
  isLoading?: boolean
}

export default function SearchWithOptionResult({ onChange, listResults, isLoading }: SearchWithOptionResultProps) {
  const t = useTranslations("owner")
  const [value, setValue] = useState("")
  const inputRef = useRef<HTMLInputElement | null>(null)
  const [isFocused, setIsFoused] = useState(false)
  useEffect(() => {
    const checkFocus = () => {
      if (!inputRef.current)
        return
      setIsFoused(document.activeElement == inputRef.current)
    }
    const inputElement = inputRef.current
    if (inputElement) {
      inputElement.addEventListener("focus", checkFocus)
      inputElement.addEventListener("blur", checkFocus)
    }
    return () => {
      if (inputElement) {
        inputElement.removeEventListener("focus", checkFocus)
        inputElement.removeEventListener("blur", checkFocus)

      }
    }
  }, [])
  return <div className="space-y-2 w-full h-9 overflow-y-visible z-10">
    <Input
      ref={inputRef}
      className="w-full h-full text-sm"
      value={value}
      onChange={e => setValue(e.target.value)}
      placeholder={t("misc.search")}
    />
    {isFocused && value.trim() != "" ?
      <div className="rounded-sm px-2 py-4 text-sm bg-foreground">
        {
          isLoading && <p className="text-center">{t('misc.loading')}</p>
        }
        {
          listResults.length > 0 ?
            <>
              {listResults.map(item => <div className="py-2 cursor-pointer hover:bg-background/50 rounded-sm" key={item.id} onChange={() => {
                setValue(item.key)
                onChange(item.key)
              }}>{item.value}</div>)}
            </>
            :
            <p className="text-center">{t('misc.noResult')}</p>
        }


      </div>
      : <></>
    }
  </div>

}

