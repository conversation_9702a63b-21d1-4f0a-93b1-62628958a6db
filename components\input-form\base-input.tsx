import { ReactNode } from "react"
import { FormControl, FormDescription, FormItem, FormLabel, FormMessage } from "../ui/form"
import { cn } from "@/lib/utils"

interface BaseInputLayoutProps {
  label?: string,
  description?: string,
  children: ReactNode,
  containerClassName?: string,
  labelClassName?: string,
  variant?: "float" | "default"
}
export default function BaseInputLayout({ children, description, label, containerClassName, labelClassName, variant = "default" }: BaseInputLayoutProps) {
  return <div className="w-full">
    <FormItem className={cn("w-full relative", variant == "float" ? "border rounded-xl focus-within:border-neutral-light px-4 py-1 !space-y-0 focus-visible:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 disabled:cursor-not-allowed" : "", containerClassName)} onClick={e => e.stopPropagation()}>
      {label &&
        <FormLabel className={labelClassName}>{label}</FormLabel>
      }
      <FormControl className='group relative w-full'>
        {children}
      </FormControl>
      {description
        &&
        <FormDescription>
          {description}
        </FormDescription>
      }
      {variant == "default" &&
        <FormMessage />
      }
    </FormItem>
    {variant == "float" &&
      <FormMessage />
    }
  </div>

}