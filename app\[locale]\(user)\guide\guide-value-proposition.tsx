import { useTranslations } from "next-intl";
import { <PERSON>, <PERSON><PERSON>in, <PERSON><PERSON><PERSON><PERSON>, Alert<PERSON>riangle } from "lucide-react";

export default function GuideValueProposition() {
  const t = useTranslations("seekers");

  const benefits = [
    {
      icon: Shield,
      title: t("guide.valueProposition.benefits.safety.title"),
      description: t("guide.valueProposition.benefits.safety.description"),
      color: "text-red-500 bg-red-50"
    },
    {
      icon: MapPin,
      title: t("guide.valueProposition.benefits.location.title"),
      description: t('guide.valueProposition.benefits.location.description'),
      color: "text-blue-500 bg-blue-50"
    },
    {
      icon: FileCheck,
      title: t("guide.valueProposition.benefits.law.title"),
      description: t("guide.valueProposition.benefits.law.description"),
      color: "text-amber-500 bg-amber-50"
    },
    {
      icon: AlertTriangle,
      title: t("guide.valueProposition.benefits.warn.title"),
      description: t("guide.valueProposition.benefits.warn.description"),
      color: "text-purple-500 bg-purple-50"
    }
  ];
  const steps = [
    {
      title: t("guide.valueProposition.steps.stepOne.title"),
      description: t("guide.valueProposition.steps.stepOne.description")
    },
    {
      title: t("guide.valueProposition.steps.stepTwo.title"),
      description: t("guide.valueProposition.steps.stepTwo.description")
    },
    {
      title: t("guide.valueProposition.steps.stepThree.title"),
      description: t("guide.valueProposition.steps.stepThree.description")
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
            {t("guide.valueProposition.title")}

          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {t("guide.valueProposition.description")}
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {benefits.map((benefit, index) => {
            const IconComponent = benefit.icon;
            return (
              <div
                key={index}
                className="bg-seekers-foreground/50 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100"
              >
                <div className={`w-16 h-16 rounded-2xl ${benefit.color} flex items-center justify-center mb-6`}>
                  <IconComponent className="h-8 w-8" />
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  {benefit.title}
                </h3>

                <p className="text-gray-600 leading-relaxed">
                  {benefit.description}
                </p>
              </div>
            );
          })}
        </div>

        {/* Additional Value Props */}
        <div className="bg-seekers-foreground/50 rounded-3xl p-8 md:p-12 shadow-xl border border-gray-100">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-6">
                {t("guide.valueProposition.whyThisGuide.title")}
              </h3>
              <div className="space-y-4 text-gray-600">
                <p className="leading-relaxed">
                  {t("guide.valueProposition.whyThisGuide.optionOne")}
                </p>
                <p className="leading-relaxed">
                  {t("guide.valueProposition.whyThisGuide.optionTwo")}
                </p>
                <p className="leading-relaxed font-semibold text-gray-900">
                  {t("guide.valueProposition.whyThisGuide.optionThree")}
                </p>
              </div>
            </div>

            <div className="bg-gradient-to-br from-seekers-primary-lightest to-neutral-lightest rounded-2xl p-8">
              <div className="space-y-6">
                {steps.map((item, idx) => <div key={idx} className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-seekers-primary rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold text-sm">{idx + 1}</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">{item.title}</h4>
                    <p className="text-sm text-gray-600">{item.description}</p>
                  </div>
                </div>)}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
