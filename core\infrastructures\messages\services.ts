import { errorHandling } from "../utils/error-handling";
import { getChatRoomDetail, getChats, updateMessageStatus } from "./api";
import { GetChatsDto } from "./dto";
import { transformMessage, transformMessageDetail } from "./transform";

export async function getChatListService(search: GetChatsDto) {
  try {
    const result = await getChats(search);
    const data = result.data.data;
    return {
      data: transformMessage(data),
      meta: undefined,
    };
  } catch (e: any) {
    console.log(e);
    const error = errorHandling(e);
    return { error };
  }
}

export async function getChatRoomDetailService(id: string) {
  try {
    if (!id)
      return {
        error: "Id required",
      };
    const result = await getChatRoomDetail(id);
    const data = result.data.data;
    return {
      data: transformMessageDetail(data),
      meta: undefined,
    };
  } catch (e: any) {
    console.log(e);
    const error = errorHandling(e);
    return { error };
  }
}

export async function updateMessageStatusService(id: string) {
  try {
    const data = await updateMessageStatus(id);
    return data;
  } catch (e: any) {
    const error = errorHandling(e);
    return { error };
  }
}
