import Compressor from "compressorjs"

export async function compressImage(file:File, options?:Compressor.Options){
  try{
    const result =  await new Promise((resolve,reject) => {
      new Compressor(file, {
      quality: 0.5,
      mimeType: "image/webp",
      success: async (response) => {
        resolve(response)
      },
      error: async (e) => {
        reject(e)
      },
      ...options,
    })
    })
    return result
  }catch(e){
    return e
  }
}

export async function fileToBase64(file:File): Promise<string>{
  return new Promise((resolve,reject) => {
    const reader = new FileReader()
    reader.onloadend = () => {
      resolve(reader.result as string)
    }
    reader.onerror = () => 
      reject(new Error("500"))
    reader.readAsDataURL(file)
  })
}