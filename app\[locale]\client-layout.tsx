"use client"
import dynamic from "next/dynamic";

const FacebookPixel = dynamic(() => import("./facebook-pixel"), { ssr: false })
const MomentLocale = dynamic(() => import("@/components/locale/moment-locale"), { ssr: false })
const CookieConsent = dynamic(() => import("@/components/cookie-consent/cookie-consent"), { ssr: false })
const NotificationProvider = dynamic(() => import("@/components/providers/notification-provider"), { ssr: false })

export default function ClientLayout() {
  return <>
    <NotificationProvider isSeeker />
    <FacebookPixel />
    <MomentLocale />
    <CookieConsent />
  </>
}