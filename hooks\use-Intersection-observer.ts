import { useEffect, useRef, useState } from "react";

export default function useIntersectionObserver(){
  const [isVisible, setIsVisible] = useState(false);
  const [firstTimeVisible, setFirstTimeVisible] = useState(true)
  const sectionRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => 
      setIsVisible(entry.isIntersecting), 
    {threshold: 0.1})

    if (sectionRef.current) observer.observe(sectionRef.current)
    return () =>{
      if (sectionRef){
        observer.disconnect()
      }
    }
  },[])

  return {isVisible, sectionRef,firstTimeVisible,setFirstTimeVisible}
}