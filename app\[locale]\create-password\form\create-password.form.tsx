"use client"
import { useToast } from "@/hooks/use-toast"
import { useLocale, useTranslations } from "next-intl"
import useChangePasswordFormSchema from "./use-change-password-form.schema"
import { z } from "zod"
import { CreatePasswordDto } from "@/core/infrastructures/auth/dto"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Form } from "@/components/ui/form"
import PasswordInput from "@/components/input-form/password-input"
import { Button } from "@/components/ui/button"
import { useRouter } from 'nextjs-toploader/app';
import { useCreatePassord } from "@/core/applications/mutations/auth/use-create-password"
import { useEffect, useState } from "react"
import { cn } from "@/lib/utils"
import { Check, X } from "lucide-react"

export default function CreatePasswordForm({ email, token }: { email: string, token: string, isSeeker?: boolean }) {
  const t = useTranslations("universal")
  const { toast } = useToast()
  const router = useRouter()
  const locale = useLocale()
  const formSchema = useChangePasswordFormSchema()
  type formSchemaType = z.infer<typeof formSchema>
  const useCreatePasswordMutation = useCreatePassord()
  const [passwordStrength, setPasswordStrength] = useState<Record<string, boolean>>({
    length: false,
    number: false,
    special: false,
    notCommon: true,
    uppercase: false
  })
  const form = useForm<formSchemaType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
      confirmPassword: ""
    }
  })
  const password = form.watch("password")

  useEffect(() => {
    if (password) {
      setPasswordStrength({
        length: password.length >= 8,
        number: /[0-9]/.test(password),
        special: /[!@#$%^&*()_+]/.test(password),
        notCommon: !["123456", "password", "qwerty"].includes(password.toLowerCase()),
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password)
      })
    }
  }, [password])



  async function onSubmit(values: formSchemaType) {
    const data: CreatePasswordDto = {
      email: email,
      token: token,
      password: values.password,
      confirm_password: values.confirmPassword,
      locale: locale
    }
    try {
      await useCreatePasswordMutation.mutateAsync(data)
      toast({
        title: t("universal.success.createPassword.title"),
        description: t("success.createPassword.description")
      })
      router.push("/")
    } catch (error: any) {
      toast({
        title: t("error.resetPassword.title"),
        description: error.response.data.message,
        variant: "destructive"
      })
    }

  }
  return <Form {...form}>
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
      <div className="space-y-2 text-center">
        <h1 className="text-2xl font-semibold text-center">{t("form.title.createPassword")}</h1>
      </div>
      <div className="space-y-2 md:min-w-80">
        <PasswordInput form={form} name="password" label={t("form.label.password")} placeholder={t('form.placeholder.basePlaceholder', { field: `${t("form.field.password")}` })} />
        <PasswordInput form={form} name="confirmPassword" label={t("form.label.confirmPassword")} placeholder={t('form.placeholder.basePlaceholder', { field: `${t("form.field.confirmPassword")}` })} />
      </div>
      {password &&
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className={cn(passwordStrength.length ? "text-green-500" : "text-red-500")}>
            {passwordStrength.length ? (
              <Check className="inline w-3 h-3 mr-1" />
            ) : (
              <X className="inline w-3 h-3 mr-1" />
            )}
            {t("form.utility.password.minimumLength")}
          </div>
          <div className={cn(passwordStrength.number ? "text-green-500" : "text-red-500")}>
            {passwordStrength.number ? (
              <Check className="inline w-3 h-3 mr-1" />
            ) : (
              <X className="inline w-3 h-3 mr-1" />
            )}
            {t("form.utility.password.numberRequired")}
          </div>
          <div className={cn(passwordStrength.special ? "text-green-500" : "text-red-500")}>
            {passwordStrength.special ? (
              <Check className="inline w-3 h-3 mr-1" />
            ) : (
              <X className="inline w-3 h-3 mr-1" />
            )}
            {t("form.utility.password.specialCharacter")}
          </div>
          <div className={cn(passwordStrength.notCommon ? "text-green-500" : "text-red-500")}>
            {passwordStrength.notCommon ? (
              <Check className="inline w-3 h-3 mr-1" />
            ) : (
              <X className="inline w-3 h-3 mr-1" />
            )}
            {t("form.utility.password.notCommonWord")}
          </div>
          <div className={cn(passwordStrength.uppercase ? "text-green-500" : "text-red-500")}>
            {passwordStrength.uppercase ? (
              <Check className="inline w-3 h-3 mr-1" />
            ) : (
              <X className="inline w-3 h-3 mr-1" />
            )}
            {t("form.utility.password.uppercaseRequired")}
          </div>
          <div className={cn(passwordStrength.lowercase ? "text-green-500" : "text-red-500")}>
            {passwordStrength.lowercase ? (
              <Check className="inline w-3 h-3 mr-1" />
            ) : (
              <X className="inline w-3 h-3 mr-1" />
            )}
            {t("form.utility.password.lowercaseRequired")}
          </div>
        </div>
      }
      <Button
        className="w-full"
        variant={"default-seekers"}
        loading={useCreatePasswordMutation.isPending}>
        {t('cta.changePassword')}
      </Button>
    </form>
  </Form >
}