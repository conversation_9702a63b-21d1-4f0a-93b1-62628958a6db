"use client"
import DialogHeader<PERSON>rapper from "@/components/dialog-wrapper/dialog-header-wrapper";
import DialogWrapper from "@/components/dialog-wrapper/dialog-wrapper";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { ImageIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useEffect, useState } from "react";

export default function ImageGalleryDialog({ imagesUrl, openDetailCarousel, setOpenCarousel, setOpenSelectedImageIndex }
  : { imagesUrl: string[], openDetailCarousel: boolean, setOpenCarousel: (val: boolean) => void, setOpenSelectedImageIndex: (val: number) => void }) {
  const t = useTranslations("seeker")
  const [open, setOpen] = useState(false)
  const [tempClose, setTempClose] = useState(openDetailCarousel)

  useEffect(() => {
    setTempClose(openDetailCarousel)
  }, [openDetailCarousel])
  return <>
    <DialogWrapper
      dialogClassName={cn("!w-[100vh] max-w-7xl !max-h-screen", openDetailCarousel ? "-z-10" : "z-50")}
      dialogOverlayClassName={cn(openDetailCarousel ? "-z-10" : "z-50")}
      open={tempClose ? false : open}
      setOpen={setOpen}
      openTrigger={
        <Button variant={"ghost"} className="absolute bottom-4 right-4 z-10 bg-white text-seekers-text-light font-medium gap-3">
          <ImageIcon className="!w-6 !h-6" />
          {t('listing.detail.images.showAllImages')}
        </Button>
      } >
      <>
        <DialogHeaderWrapper>
          <h2 className="text-base font-bold text-seekers-text text-center ">{t('listing.detail.images.title')}</h2>
        </DialogHeaderWrapper>
        <div className="grid md:grid-cols-3 gap-3 relative overflow-auto h-fit">
          {imagesUrl.map((item, idx) => <div key={idx}
            className="relative rounded-lg aspect-[4/3] h-fit">
            <div className="relative w-full h-full overflow-hidden rounded-lg">
              <Image src={item} alt=""
                loading="lazy"
                fill
                style={{ objectFit: "cover" }}
                onClick={() => {
                  setOpenCarousel(true)
                  setOpenSelectedImageIndex(idx)
                }} />
            </div>
          </div>
          )}
        </div>
      </>

    </DialogWrapper>
  </>

}