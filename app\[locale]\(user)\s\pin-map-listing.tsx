"use client"
import { ListingListSeekers } from "@/core/domain/listing/listing-seekers";
import { AdvancedMarker, InfoWindow, useAdvancedMarkerRef, } from "@vis.gl/react-google-maps";
import { ListingImage, ListingPrice, ListingSellingPoint, ListingTitle, ListingWrapper } from "../(listings)/listing-item";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { useSeekersSearchMapUtil } from "@/stores/seekers-search-map-utils";
import ListingCategoryIcon from "./listing-category-icon";
import { cn } from "@/lib/utils";

export default function PinMapListings(
  {
    data,
    conversions
  }:
    {
      data: ListingListSeekers,
      conversions: { [key: string]: number }
    }) {
  const [markerRef, marker] = useAdvancedMarkerRef();
  const { focusedListing, setFocusedListing, highlightedListing, } = useSeekersSearchMapUtil()

  return <>
    <AdvancedMarker

      key={data.code}
      position={{ lat: data.geolocation[0], lng: data.geolocation[1] }}
      clickable
      onClick={() => setFocusedListing(data.code)}
      ref={markerRef}
      zIndex={highlightedListing == data.code ? 10 : 1}
    >
      <div className={cn(
        highlightedListing == data.code ? "w-12 h-12 bg-seekers-text text-white" : "w-6 h-6 bg-white",
        "flex items-center justify-center py-3 rounded-full text-sm font-medium shadow-md border")}>
        <ListingCategoryIcon category={data.category || ""} className={highlightedListing == data.code ? "" : "!w-4 !h-4 text-seekers-primary"} />
      </div>
    </AdvancedMarker>
    {focusedListing == data.code && <>
      <InfoWindow
        anchor={marker}
        onClose={() => setFocusedListing(null)}
        headerDisabled
        style={{
          overflow: "auto !important",
          borderRadius: "24px !important"
        }}
        className="!rounded-xl"
        minWidth={240}
      >
        <ListingWrapper conversion={conversions} data={data}>
          <ListingImage forceLazyloading containerClassName="!rounded-b-none" extraHeaderAction={<>
            <Button variant={"secondary"} className="bg-white rounded-full !h-6 !w-6 hover:bg-white/80" size={"icon"} onClick={() => setFocusedListing(null)}>
              <X className="!w-3 !h-3" />
            </Button>
          </>} />
          <div className="space-y-2 px-2 pb-2">
            <ListingTitle className="leading-6" />
            <ListingSellingPoint />
            <ListingPrice />
          </div>
        </ListingWrapper>
      </InfoWindow>
    </>}
  </>
}