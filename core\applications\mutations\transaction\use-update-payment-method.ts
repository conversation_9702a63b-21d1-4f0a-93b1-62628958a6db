import { putPaymentMethod } from "@/core/infrastructures/transaction/api";
import { PutPaymentMethod } from "@/core/infrastructures/transaction/dto";
import { useMutation } from "@tanstack/react-query";

export function useUpdatePaymentMethod() {
  const mutation = useMutation({
    mutationFn: (data: PutPaymentMethod) => putPaymentMethod(data),
    onSuccess: (response) => {
      // window.location.href = response.data.data.url
      return response;
    },
  });

  return mutation;
}
