"use client"
import { useTranslations } from "next-intl"
import { FC, useEffect, useState } from "react"
import { useSignUpSeekerFormSchema } from "../../(auth)/form/use-sign-up-form.schema"
import { z } from "zod"
import { useRegisterStore } from "@/stores/register.store"
import { useToast } from "@/hooks/use-toast"
import { useEmailVerification } from "@/core/applications/mutations/auth/use-email-verification"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import moment from "moment"
import { RegisterUserDto } from "@/core/infrastructures/user/dto"
import { PROPERTY_SEEKERS } from "@/lib/constanta/constant"
import { Form } from "@/components/ui/form"
import DefaultInput from "@/components/input-form/default-input"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import SeekersSocialAuthentication from "./seekers-social-authentication"
import { cn } from "@/lib/utils"
import { Check, X, Eye, EyeOff } from "lucide-react"
import PasswordInput from "@/components/input-form/password-input"

export const UserSignUpForm: FC<{ onClickLogin?: () => void, onSuccess: () => void }> = ({ onClickLogin, onSuccess }) => {
  const t = useTranslations("seeker")
  const formSchema = useSignUpSeekerFormSchema()
  type formSchemaType = z.infer<typeof formSchema>
  const { setRegister, setValidFormUntil, register } = useRegisterStore()
  const [passwordStrength, setPasswordStrength] = useState<Record<string, boolean>>({
    length: false,
    number: false,
    special: false,
    notCommon: true,
    uppercase: false
  })
  const { toast } = useToast()
  const useSendOtpViaEmail = useEmailVerification((response) => {
    if ((response as any)?.response?.data?.message !== "Email verification code is already sent. Please check your email") {
      toast({
        title: t('success.sendVerification.title') + " " + register.email
      })
    }
    onSuccess()
  })
  const form = useForm<formSchemaType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      confirmPassword: register.confirm_password || "",
      contact: register.email || "",
      firstName: register.first_name || "",
      lastName: register.last_name || "",
      password: register.password || ""
    },
  })
  const password = form.watch("password")
  useEffect(() => {
    if (password) {
      setPasswordStrength({
        length: password.length >= 8,
        number: /[0-9]/.test(password),
        special: /[!@#$%^&*()_+]/.test(password),
        notCommon: !["123456", "password", "qwerty"].includes(password.toLowerCase()),
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password)
      })
    }
  }, [password])
  async function onSubmit(values: z.infer<typeof formSchema>) {
    const date = moment().add(30, "minutes")
    const data: RegisterUserDto = {
      email: values.contact || "",
      password: values.password,
      confirm_password: values.confirmPassword,
      first_name: values.firstName.trim(),
      last_name: values.lastName.trim(),
      type: PROPERTY_SEEKERS,
      otp: "00000"
    }
    setRegister(data)
    setValidFormUntil(date)
    try {
      await useSendOtpViaEmail.mutateAsync({ email: data.email!, category: "REGISTRATION" })
    } catch (error: any) {
      if ((error?.response?.data?.message as string) == "Email verification code is already sent. Please check your email") return
      toast({
        title: t("message.otpRequest.failedToast.title"),
        description: error?.response?.data?.message || "",
        variant: "destructive"
      })
    }

    return

  }
  return <Form {...form}>
    <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-4">
      <div className="grid gap-4">
        <div className="grid grid-cols-2 gap-4">
          <DefaultInput
            form={form}
            label={t("form.label.firstName")}
            name="firstName"
            placeholder=""
            type="text"
            variant="float"
            labelClassName="text-xs text-seekers-text-light font-normal"
          />
          <DefaultInput
            form={form}
            label={t("form.label.lastName")}
            name="lastName"
            placeholder=""
            type="text"
            variant="float"
            labelClassName="text-xs text-seekers-text-light font-normal"
          />
        </div>
        <DefaultInput
          form={form}
          label={t("form.label.email")}
          name="contact"
          placeholder=""
          type="email"
          variant="float"
          labelClassName="text-xs text-seekers-text-light font-normal"
        />


        <div className="grid md:grid-cols-2 gap-6">
          <PasswordInput
            form={form}
            name="password"
            variant="float"
            label={t("form.label.password")}
            placeholder={""}
            inputProps={{
              required: true
            }}
            labelClassName="text-xs text-seekers-text-light font-normal"

          />

          <PasswordInput
            form={form}
            name="confirmPassword"
            variant="float"
            label={t("form.label.confirmPassword")}
            placeholder={""}
            inputProps={{
              required: true
            }}
            labelClassName="text-xs text-seekers-text-light font-normal"

          />
        </div>

        {password &&
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className={cn(passwordStrength.length ? "text-green-500" : "text-red-500")}>
              {passwordStrength.length ? (
                <Check className="inline w-3 h-3 mr-1" />
              ) : (
                <X className="inline w-3 h-3 mr-1" />
              )}
              {t("form.utility.password.minimumLength")}
            </div>
            <div className={cn(passwordStrength.number ? "text-green-500" : "text-red-500")}>
              {passwordStrength.number ? (
                <Check className="inline w-3 h-3 mr-1" />
              ) : (
                <X className="inline w-3 h-3 mr-1" />
              )}
              {t("form.utility.password.numberRequired")}
            </div>
            <div className={cn(passwordStrength.special ? "text-green-500" : "text-red-500")}>
              {passwordStrength.special ? (
                <Check className="inline w-3 h-3 mr-1" />
              ) : (
                <X className="inline w-3 h-3 mr-1" />
              )}
              {t("form.utility.password.specialCharacter")}
            </div>
            <div className={cn(passwordStrength.notCommon ? "text-green-500" : "text-red-500")}>
              {passwordStrength.notCommon ? (
                <Check className="inline w-3 h-3 mr-1" />
              ) : (
                <X className="inline w-3 h-3 mr-1" />
              )}
              {t("form.utility.password.notCommonWord")}
            </div>
            <div className={cn(passwordStrength.uppercase ? "text-green-500" : "text-red-500")}>
              {passwordStrength.uppercase ? (
                <Check className="inline w-3 h-3 mr-1" />
              ) : (
                <X className="inline w-3 h-3 mr-1" />
              )}
              {t("form.utility.password.uppercaseRequired")}
            </div>
            <div className={cn(passwordStrength.lowercase ? "text-green-500" : "text-red-500")}>
              {passwordStrength.lowercase ? (
                <Check className="inline w-3 h-3 mr-1" />
              ) : (
                <X className="inline w-3 h-3 mr-1" />
              )}
              {t("form.utility.password.lowercaseRequired")}
            </div>
          </div>
        }
      </div>

      <Button className="w-full" variant={"default-seekers"} loading={useSendOtpViaEmail.isPending}>
        {t("cta.createAccount")}
      </Button>

      <div className="mt-4 text-center">
        <p className="text-sm text-gray-500">
          {t("auth.alreadyHaveAccount")}{" "}
          <Button
            variant={"link"}
            onClick={onClickLogin}
            className="p-0 h-9 text-seekers-primary hover:underline"
          >
            {t("cta.login")}
          </Button>
        </p>
      </div>

      <div className="relative my-6">
        <Separator />
        <span className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-background px-2 text-sm text-muted-foreground">
          {t('conjuntion.or')}
        </span>
      </div>

      <SeekersSocialAuthentication />
    </form>
  </Form>
}