import { ListingContractType } from "@/core/domain/listing/listing";
import SaveAction from "./save-action";
import ShareAction from "./share-action";
import { ItemWithSuffix } from "@/core/domain/utils/utils";
import PriceInfo from "./price-info";
import RentMinimumDuration from "./rent-minimum-duration";
import ContactOwner from "./contact-owner";
import { useTranslations } from "next-intl";
import PropertyOwner from "./property-owner";
import { Badge } from "@/components/ui/badge";
import AvailableAt from "./available-at";
import RentMaximumDuration from "./rent-maximum-duration";

export default function DesktopPropertyAction({ availableAt, price, type, minDuration, maxDuration, propertyId, owner, isFavorited, isNegotiable, middleman, isActiveListing = true, chatCount = 0 }: {
  price: number,
  type: ListingContractType,
  availableAt: string | null
  minDuration: ItemWithSuffix,
  maxDuration: ItemWithSuffix,
  propertyId: string,
  owner?: {
    ownerId: string,
    ownerName: string,
    ownerProfileUrl: string
  },
  middleman?: {
    middlemanId: string,
    middlemanName: string,
    middlemanProfileUrl: string
  }
  isFavorited?: boolean,
  currency?: string,
  chatCount?: number,
  isNegotiable?: boolean,
  isActiveListing?: boolean
}) {
  const t = useTranslations("seeker")
  return <div className="max-sm:hidden w-full space-y-6 sticky top-0">
    <div className="flex justify-end items-center gap-6">
      <ShareAction />
      <SaveAction propertyId={propertyId} isFavorited={isFavorited} />
    </div>

    <div className="p-4 lg:p-6 space-y-6 rounded-lg border border-seekers-text-lighter">
      <Badge variant={"seekers"}>{type}</Badge>
      <PriceInfo price={price} type={type} maxDuration={maxDuration} minDuration={minDuration} isNegotiable={isNegotiable} />
      <AvailableAt availableAt={availableAt} />
      {type == "RENT" &&
        <div className="border-t border-t-seekers-text-lighter border-b border-b-seekers-text-lighter py-4">
          <p className="text-xs font-semibold text-seekers-text-light !mb-2 ">{t('misc.rentalTerms')}</p>
          <RentMinimumDuration type={type} minDuration={minDuration} />
          <RentMaximumDuration type={type} maxDuration={maxDuration} minDuration={minDuration} />
        </div>
      }

      {owner && <PropertyOwner
        owner={{ name: owner.ownerName || t("misc.ownerProperty"), image: owner.ownerProfileUrl }}
        middleman={middleman ? { name: middleman?.middlemanName, image: middleman.middlemanProfileUrl } : undefined}
      />
      }
      <div className="space-y-2">
        <ContactOwner ownerId={owner?.ownerId || ""} middlemanId={middleman?.middlemanId} propertyId={propertyId} isActiveListing={isActiveListing} />
        {chatCount > 0 &&
          <p className="text-center text-xs font-medium text-seekers-text-light">{t('listing.detail.contactCount', { count: chatCount })}</p>
        }
      </div>

    </div>
  </div>

}