import { cn } from "@/lib/utils"
import { useTranslations } from "next-intl"

export default function PackageWrapper({ children, isMostPopular }: { children: React.ReactNode, isMostPopular?: boolean }) {
  const t = useTranslations("seeker")
  return <div className={cn(
    "rounded-lg border shadow-sm relative",
    isMostPopular ? "border-seekers-primary-light bg-seekers-primary/5" : "bg-background"
  )}>
    {isMostPopular && (
      <div className="absolute -top-3 left-0 right-0 flex justify-center">
        <div className="rounded-md bg-[#B88C5B] px-3 py-1 text-xs font-medium text-white uppercase">★ {t('misc.mostPopular')}</div>
      </div>
    )}
    {children}
  </div>
}
