"use client"

import { MessageTextDto } from "@/core/infrastructures/messages/dto"
import { transformMessageText } from "@/core/infrastructures/messages/transform"
import { socket } from "@/core/utils/socket"
import { useToast } from "@/hooks/use-toast"
import { useMessagingStore } from "@/stores/messaging.store"
import { useSettingStore } from "@/stores/setting.store"
import { useEffect, useState } from "react"
import DialogWrapper from "../dialog-wrapper/dialog-wrapper"
import { DialogTitle } from "@radix-ui/react-dialog"
import { Button } from "../ui/button"
import { useNotification } from "@/hooks/use-notification"
import DialogHeaderWrapper from "../dialog-wrapper/dialog-header-wrapper"
import { useTranslations } from "next-intl"
import { useUserStore } from "@/stores/user.store"

export default function NotificationProvider({ isSeeker = false }: { isSeeker?: boolean }) {
  const t = useTranslations("universal")
  const { toast } = useToast()
  const [open, setOpen] = useState(false)
  const { seekers, hydrated } = useUserStore()

  const { updatechatDetail, updateSpecificAllChat } = useMessagingStore(state => state)
  const { hasNotificationSound, isLoading } = useSettingStore(state => state)
  const { enableSoundNotification, playSound, popUpNotification } = useNotification()
  useEffect(() => {
    // if (!hydrated) return
    // if (seekers.email !== "" && seekers.accounts.membership == "Free") return
    if (!socket.connected) {
      socket.connect()
    }
    const receiveMessageHandler = (data: MessageTextDto) => {
      // if(!hasNotificationSound) return
      const message = transformMessageText(data);
      toast({
        title: "new message from " + message.displayName,
        description: message.text
      });
      window.dispatchEvent(new CustomEvent("newMessage", { detail: message }));
    };

    socket.on("newChatNotif", receiveMessageHandler);

    // Clean up to remove listeners and prevent duplicate subscriptions
    return () => {
      socket.off("newChatNotif", receiveMessageHandler);
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  // }, [seekers.email, seekers.accounts.membership])
  useEffect(() => {
    const handleNewMessage = (event: any) => {
      playSound()
      popUpNotification("new messsage from " + event.detail.displayName, event.detail.text)
      updatechatDetail(event.detail);
      updateSpecificAllChat(event.detail)

    }
    // Listen for custom "newMessage" event
    window.addEventListener("newMessage", handleNewMessage)

    return () => {
      window.removeEventListener("newMessage", handleNewMessage)
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [playSound])

  useEffect(() => {
    if (isLoading) return
    if (hasNotificationSound == undefined) {
      setOpen(true)
    } else {
      setOpen(false)
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasNotificationSound])

  return <>
    {/* Temporary comment this  */}
    {/* <DialogWrapper
      open={open}
      setOpen={setOpen}
      openTrigger={<></>}
      dialogClassName="lg:max-w-sm"
    >
      <DialogHeaderWrapper className="text-start px-0">
        <DialogTitle className="font-semibold">{t('misc.enableSoundNotification.title')}</DialogTitle>
      </DialogHeaderWrapper>
      <div className="space-y-4 ">
        <p className="max-sm:text-center max-sm:max-w-[256px] max-sm:mx-auto">{t('misc.enableSoundNotification.description')}</p>
        <div className="flex max-sm:flex-col gap-2">
          <Button variant={"ghost"} className="max-sm:w-full max-sm:order-last" onClick={() => {
            enableSoundNotification(false)
            setOpen(false)
          }}>{t('cta.disable')}</Button>
          <Button
            className="max-sm:w-full max-sm:order-first"
            variant={isSeeker ? "default-seekers" : 'default'}
            onClick={() => {
              enableSoundNotification(true)
              setOpen(false)

            }}>{t('cta.enable')}</Button>
        </div>
      </div>
    </DialogWrapper> */}
  </>
}