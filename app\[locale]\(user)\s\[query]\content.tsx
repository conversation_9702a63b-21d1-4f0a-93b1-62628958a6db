"use client"
import { ListingImage, ListingLoader, ListingLocation, ListingPrice, ListingSellingPoint, ListingTitle, ListingWrapper } from "../../(listings)/listing-item"
import { DataPagination } from "@/components/utility/seekers-pagination"
import { useTranslations } from "next-intl"
import { useGetFilteredSeekersListing } from "@/core/applications/queries/listing/use-get-filtered-seeker-listing"
import { useSeekerFilterResultStore } from "@/stores/seeker-filter-result.store"
import { useEffect } from "react"
import { SearchParams } from "@/hooks/use-seekers-filter"
import { useSeekersSearchMapUtil } from "@/stores/seekers-search-map-utils"
export const MAX_LIMIT = "16"
export const INFINITY_LIMIT = "9999"

export default function ContentSearch({ query, types, conversions, ...rest }: SearchParams & { conversions: { [key: string]: number } }) {
  const t = useTranslations("seeker")
  const store = useSeekerFilterResultStore()
  const setHighlightedListing = useSeekersSearchMapUtil(state => state.setHighlightedListing)
  const { query: properties } = useGetFilteredSeekersListing({
    page: rest.page.toString(),
    per_page: rest.perPage || MAX_LIMIT,
    search: query?.replaceAll("--", " ").replaceAll("-", ", "),
    type: types.split(","),
    bathroom_total: rest.bathroomTotal,
    bedroom_total: rest.bedroomTotal,
    max_price: rest.maxPrice,
    min_price: rest.minPrice == 0 ? 1 : rest.minPrice,
    years_of_building: rest.yearsOfBuilding,
    area: rest.lat && rest.lng ? {
      latitude: rest.lat,
      longitude: rest.lng,
      zoom: rest.zoom || "13"
    } : undefined,
    rental_offers: rest.rentalOffers?.split(","),
    selling_points: rest.sellingPoints?.split(","),
    features: rest.feature?.split(","),
    sort_by: rest.sortBy,
    building_largest: rest.buildingLargest,
    building_smallest: rest.buildingSmallest == 0 ? 1 : rest.buildingSmallest,
    garden_largest: rest.gardenLargest,
    garden_smallest: rest.gardenSmallest == 0 ? 1 : rest.gardenSmallest,
    land_largest: rest.LandLargest,
    land_smallest: rest.LandSmallest == 0 ? 1 : rest.LandSmallest,
    electricity: rest.electricity,
    parking_option: rest.parkingOption,
    pool_option: rest.poolOption,
    living_option: rest.typeLiving,
    furnishing_option: rest.furnishingOption,
    property_of_view: rest.propertyOfView?.split(","),
    location_type: rest.propertyLocation,
    contract_duration: rest.minimumContract,
  }, true)

  useEffect(() => {
    if (properties.isError) return store.setIsLoading(false)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [properties.isError])
  useEffect(() => {

    store.setIsLoading(properties.isPending)
    if (!properties.isSuccess) return
    store.setData(properties.data?.data || [])
    store.setTotal(properties.data.meta?.total || 0)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [properties.data?.data]) // don't include store on depedencies, it cause infinity rendering\


  return <>
    <section className="min-h-[calc(100vh-202px)]">
      <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-3 max-sm:px-4 max-sm:my-4 md:mr-6 gap-x-3 gap-y-6">
        {properties.isPending ?
          Array(12).fill(0).map((_, idx) => <ListingLoader key={idx} />)
          :
          store.data && store.data.length > 0 ?
            <>
              {store.data.map((item, idx) => <div
                key={idx}
                onMouseEnter={() => {
                  setHighlightedListing(item.code)
                }
                }>
                <ListingWrapper
                  className="space-y-3"
                  data={item}
                  conversion={conversions}
                >
                  <ListingImage heartSize="large" />
                  <div className="space-y-2 px-0.5">
                    <div>
                      <ListingTitle />
                      <ListingSellingPoint className="!-mt-1" />
                    </div>
                    <ListingLocation className="text-seekers-text" />
                    <ListingPrice />
                  </div>
                </ListingWrapper>
              </div>
              )}

            </>
            : <>
              <p className="col-span-full text-center font-semibold py-8">{t('listing.misc.propertyNotFound')}</p>
            </>
        }
      </div >
    </section>
    <section className="!mt-12">
      {
        properties.isPending || (properties.data?.data?.length && properties.data?.data?.length < +MAX_LIMIT && properties.data.meta.pageCount == 1) ? <></>
          :
          <div className="w-fit mx-auto">
            <DataPagination meta={properties?.data?.meta}
              totalThreshold={+MAX_LIMIT}
              disableRowPerPage />
          </div>
      }
    </section>
  </>
}