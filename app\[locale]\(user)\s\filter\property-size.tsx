import FilterContentLayout from "@/app/[locale]/(user)/s/filter/filter-content-layout";
import PriceRangeSliderItem from "@/app/[locale]/(user)/s/filter/range-slider-item";
import { Label } from "@/components/ui/label";
import { useGetFilterParameter } from "@/core/applications/queries/listing/use-get-filter-parameters";
import { seekersListingFilterType } from "@/core/domain/listing/listing-seekers";
import useSearchParamWrapper from "@/hooks/use-search-param-wrapper";
import { filterTitles } from "@/lib/constanta/constant";
import { useSeekerFilterStore } from "@/stores/seekers-filter-2.store";
import { useTranslations } from "next-intl";
import { ComponentProps, useEffect } from "react";

export default function PropertySizeFilter() {
  const t = useTranslations("seeker")
  const { buildingSize, gardenSize, landSize, setBuildingSize, setGardenSize, setLandSize, typeProperty } = useSeekerFilterStore(state => state)
  const { searchParams } = useSearchParamWrapper()

  const filterParameterQuery = useGetFilterParameter()

  const maxLandParams = searchParams.get(filterTitles.landLargest)
  const minLandParams = searchParams.get(filterTitles.landSmallest)

  const maxBuildingParams = searchParams.get(filterTitles.buildingLargest)
  const minBuildingParams = searchParams.get(filterTitles.buildingSmallest)

  const maxGardenParams = searchParams.get(filterTitles.gardenLargest)
  const minGardenParams = searchParams.get(filterTitles.gardenSmallest)
  useEffect(() => {
    if (filterParameterQuery.isPending) return
    const landSizeValue = filterParameterQuery.data?.data?.landSizeRange
    const buildingSizeValue = filterParameterQuery.data?.data?.buildingSizeRange
    const gardenSizeValue = filterParameterQuery.data?.data?.gardenSizeRange

    if (landSizeValue) {
      setLandSize(+(minLandParams || landSizeValue.min), +(maxLandParams || landSizeValue.max))
    }
    if (buildingSizeValue) {
      setBuildingSize(+(minBuildingParams || buildingSizeValue.min), +(maxBuildingParams || buildingSizeValue.max))
    }
    if (gardenSizeValue) {
      setGardenSize(+(minGardenParams || gardenSizeValue.min), +(maxGardenParams || gardenSizeValue.max))
    }

  }, [
    filterParameterQuery.isPending,
    filterParameterQuery.data?.data?.buildingSizeRange,
    filterParameterQuery.data?.data?.gardenSizeRange,
    filterParameterQuery.data?.data?.landSizeRange,
    setLandSize,
    setBuildingSize,
    setGardenSize,
    minGardenParams, maxGardenParams,
    minBuildingParams, maxBuildingParams,
    minLandParams, maxLandParams
  ])



  return <FilterContentLayout title={t('listing.filter.propertySize.title')}>
    <PropertySizeContentLayout title={t('listing.filter.propertySize.landSize.title')}>
      <PriceRangeSliderItem
        min={filterParameterQuery.data?.data?.landSizeRange.min}
        max={filterParameterQuery.data?.data?.landSizeRange.max}
        rangeValue={landSize}
        onRangeValueChange={setLandSize}
      />
    </PropertySizeContentLayout>
    {![seekersListingFilterType.land, seekersListingFilterType.business].includes(typeProperty as any) && <>
      <PropertySizeContentLayout title={t('listing.filter.propertySize.buildingSize.title')}>
        <PriceRangeSliderItem
          min={filterParameterQuery.data?.data?.buildingSizeRange.min}
          max={filterParameterQuery.data?.data?.buildingSizeRange.max}
          rangeValue={buildingSize}
          onRangeValueChange={setBuildingSize}
        />
      </PropertySizeContentLayout>
    </>
    }
    {
      seekersListingFilterType.land !== typeProperty &&
      <PropertySizeContentLayout title={t('listing.filter.propertySize.gardenSize.title')}>
        <PriceRangeSliderItem
          min={filterParameterQuery.data?.data?.gardenSizeRange.min}
          max={filterParameterQuery.data?.data?.gardenSizeRange.max}
          rangeValue={gardenSize}
          onRangeValueChange={setGardenSize}
        />
      </PropertySizeContentLayout>
    }
  </FilterContentLayout>
}

interface PropertySizeContentLayoutProps extends ComponentProps<"div"> {
  children: React.ReactNode,
  title: string
}
function PropertySizeContentLayout({ children, title }: PropertySizeContentLayoutProps) {
  return <div className="grid grid-cols-12 gap-2 justify-between">
    <Label className="max-sm:col-span-12 col-span-4 font-normal">{title} ( m<span className="align-super">2</span> )</Label>
    <div className="max-sm:col-span-12 col-span-8">
      {children}
    </div>
  </div>
}