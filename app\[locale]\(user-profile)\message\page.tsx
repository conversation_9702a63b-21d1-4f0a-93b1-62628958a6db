import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import MessageBreadCrumb from "./bread-crumb";
import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import ChatList from "./chat-list";
import { ChatDetail } from "./chat-detail";
import ParticipantDetail from "./participant-detail";
import { seekersMessageUrl } from "@/lib/constanta/route";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("seeker")
  return {
    title: t("metadata.message.title"),
    description: t("metadata.message.description"),
    alternates: {
      languages: {
        en: process.env.USER_DOMAIN + "/en" + seekersMessageUrl,
        id: process.env.USER_DOMAIN + "/id" + seekersMessageUrl
      }
    }
  }
}



export default function MessagePage() {
  return <div className="h-full max-sm:space-y-6 md:flex md:flex-col items-start md:overflow-hidden max-h-full md:gap-6 ">
    <MessageBreadCrumb />
    <MainContentLayout className="flex gap-8 space-y-0 w-full max-sm:pb-4 md:max-h-[calc(100%-68px-24px)] flex-grow max-md:px-0 md:pr-0"> {/* calculation formula: 100% - breadcrumbHeight - gap */}
      <ChatList />
      <ChatDetail />
      <ParticipantDetail />
    </MainContentLayout>
  </div>
}