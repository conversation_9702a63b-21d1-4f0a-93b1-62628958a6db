import { RegisterUserDto } from "@/core/infrastructures/user/dto";
import { stat } from "fs";
import moment from "moment";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

interface RegisterState {
  register: RegisterUserDto;
  setRegister: (value: RegisterUserDto) => void;
  verifyOtpType: "EMAIL" | "PHONE_NUMBER" | "";
  setVerifyOtpType: (value: "EMAIL" | "PHONE_NUMBER") => void;
  reset: () => void;
  validFormUntil?: moment.Moment;
  setValidFormUntil: (value?: moment.Moment) => void;
  successSignUp: boolean;
  setSuccessSignUp: (value: boolean) => void;
  loading: boolean;
  setLoading: (val: boolean) => void;
}
const defaultRegister: RegisterUserDto = {
  confirm_password: "",
  first_name: "",
  last_name: "",
  otp: "",
  password: "",
  type: "SEEKER",
  email: "",
  phone_code: "+62",
  phone_number: "",
};

export const useRegisterStore = create<RegisterState>()(
  persist(
    (set) => ({
      register: defaultRegister,
      setRegister: (register) => set({ register }),
      verifyOtpType: "",
      setVerifyOtpType: (verifyOtpType) => set({ verifyOtpType }),
      reset: () => {
        useRegisterStore.persist.clearStorage();
        set({ register: defaultRegister });
      },
      validFormUntil: undefined,
      setValidFormUntil: (value?: moment.Moment) =>
        set({ validFormUntil: value }),
      successSignUp: false,
      setSuccessSignUp: (val) => set({ successSignUp: val }),
      loading: true,
      setLoading: (val) => set({ loading: val }),
    }),
    {
      name: "register-user",
      storage: createJSONStorage(() => localStorage),
      onRehydrateStorage: () => (state) => {
        if (state?.validFormUntil) {
          const validDate = moment(state?.validFormUntil);
          const currentDate = moment();
          if (currentDate.isAfter(validDate)) {
            state.setRegister(defaultRegister);
          }
        }
        state?.setLoading(false);
      },
    }
  )
);
