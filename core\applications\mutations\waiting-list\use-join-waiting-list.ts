import { joinWaitingList } from "@/core/infrastructures/waiting-list/api";
import { WaitingListDto } from "@/core/infrastructures/waiting-list/dto";
import { useToast } from "@/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

export function useJoinWaitingList() {
  const { toast } = useToast();
  const t = useTranslations("seeker");
  const mutation = useMutation({
    mutationFn: (data: WaitingListDto) => joinWaitingList(data),
    onError: (error) => {
      const data: any = (error as any).response.data;
      toast({
        title: t("misc.foundError"),
        description: data.message,
        variant: "destructive",
      });
    },
  });
  return mutation;
}
