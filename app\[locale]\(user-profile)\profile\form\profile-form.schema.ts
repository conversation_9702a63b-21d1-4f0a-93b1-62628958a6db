import { useTranslations } from "next-intl"
import phone from "phone"
import { z } from "zod"

export default function useProfileFormSchema() {
  const t = useTranslations("seeker")
  const formSchema = z.object({
    id: z.string(),
    firstName: z.string().min(1, {
      message: t("form.utility.fieldRequired", { field: t("form.field.firstName") }),
    }),
    lastName: z.string().min(1, {
      message: t("form.utility.fieldRequired", { field: t("form.field.lastName") }),
    }),
    email: z.string().email({
        message: t("form.utility.enterValidField",
          { field: ` ${t("form.field.email")}` })
        })
        .refine(value => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return emailRegex.test(value)
    },
      {
        message: t("form.utility.enterValidField",
          { field: ` ${t("form.field.email")}` })
      }),
    phoneNumber: z.string().refine(value => {
          const phoneChecker = phone(value)
          return phoneChecker.isValid
        },
          {
            message: t("form.utility.enterValidField",
              { field: `${t("form.field.phoneNumber")} ` })
      })
  })
  return formSchema
}