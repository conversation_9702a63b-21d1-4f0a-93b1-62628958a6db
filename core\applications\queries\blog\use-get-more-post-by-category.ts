import { getHomepagePosts, getPostByCategory} from "@/core/services/sanity/services"
import { useQuery } from "@tanstack/react-query"

export const GET_POSTS_QUERY_KEY = "get-post"
export function useGetMorePostByCategory(category:string,id:string | number,enable:boolean = false){
  const query = useQuery({
    queryKey: [GET_POSTS_QUERY_KEY],
    queryFn: async () => await getPostByCategory(category,id.toString()),
    enabled:category == "" ? false : enable
    
  })
  return query
}