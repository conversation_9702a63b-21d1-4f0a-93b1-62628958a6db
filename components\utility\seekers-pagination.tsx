import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon,
} from "@radix-ui/react-icons"

import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { usePaginationRequest } from "@/hooks/use-pagination-request"
import { BaseMeta } from "@/core/domain/utils/utils"
import { useEffect, useState } from "react"
import { useTranslations } from "next-intl"

export interface DataPaginationProps {
  meta?: BaseMeta,
  disableRowPerPage?: boolean,
  totalThreshold?: number,
  totalPageThreshold?: number
}

export function DataPagination({
  meta,
  disableRowPerPage,
  totalThreshold = 10,
  totalPageThreshold = 1
}: DataPaginationProps) {
  const t = useTranslations("seeker")
  const { page, perPage, setPageSearch, setPerPageSearch } = usePaginationRequest(meta?.page, meta?.perPage)
  const [disabledPrev, setDisablePrev] = useState(false)
  const [disabledNext, setDisableNext] = useState(false)
  const [forceDisablePagination, setForceDisablePagination] = useState(false)
  const [forceDisableRowPerPage, setForceDisableRowPerPage] = useState(disableRowPerPage)
  useEffect(() => {
    setDisablePrev(!meta?.prevPage)
    setDisableNext(!meta?.nextPage)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [meta?.prevPage, meta?.nextPage])
  const setToFirstPage = () => {
    setPageSearch(1)
  }
  const setPerTableSize = (value: string) => {
    setPerPageSearch(+value)
  }
  const setPrevPage = () => {
    setPageSearch(+page - 1)

  }
  const setNextPage = () => {
    setPageSearch(+page + 1)
  }
  const setToLastPage = () => {
    setPageSearch(meta?.pageCount || 1)
  }
  useEffect(() => {
    const totalPage = +(meta?.pageCount || 1)
    const total = +(meta?.total || 0)
    if (totalPage <= totalPageThreshold && total < totalThreshold) {
      setForceDisablePagination(true)
    } else {
      setForceDisablePagination(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [meta?.pageCount])

  return (
    <div className="flex max-sm:flex-col max-sm:items-start items-center justify-end px-2 w-full flex-wrap">
      <div className="flex items-center lg:space-x-8">
        <div className="flex items-center md:space-x-2">
          {forceDisableRowPerPage ? <></>
            : <>
              <p className="text-sm font-medium max-sm:hidden">{t('component.pagination.rowPerPage')} {" "}</p>
              <Select
                value={perPage.toString()}
                onValueChange={(value) => {
                  setPerTableSize(value)
                }}
              >
                <SelectTrigger className="h-8 w-[70px]">
                  <SelectValue placeholder={10} />
                </SelectTrigger>
                <SelectContent side="top">
                  {[10, 20, 30, 40, 50].map((pageSize) => (
                    <SelectItem key={pageSize} value={`${pageSize}`}>
                      {pageSize}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </>
          }
        </div>
        {forceDisablePagination ? <> </> :

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => setToFirstPage()}
              disabled={disabledPrev}
            >
              <span className="sr-only">{t('component.pagination.goToFirstPage')}</span>
              <DoubleArrowLeftIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => setPrevPage()}
              disabled={disabledPrev}
            >
              <span className="sr-only">{t('component.pagination.goToPreviousPage')}</span>
              <ChevronLeftIcon className="h-4 w-4" />
            </Button>
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              {t('misc.page')} {meta?.page || 1} {t('conjuntion.of')} {" "}
              {meta?.pageCount || 1}
            </div>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => setNextPage()}
              disabled={disabledNext}
            >
              <span className="sr-only">{t('component.pagination.goToNextPage')}</span>
              <ChevronRightIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => setToLastPage()}
              disabled={disabledNext}
            >
              <span className="sr-only">{t('component.pagination.goToLastPage')}</span>
              <DoubleArrowRightIcon className="h-4 w-4" />
            </Button>
          </div>
        }

      </div>
    </div>
  )
}
