import { useState } from "react";
import DialogWrapper from "../dialog-wrapper/dialog-wrapper";
import { Button } from "../ui/button";
import { Globe } from "lucide-react";
import DialogHeaderWrapper from "../dialog-wrapper/dialog-header-wrapper";
import { useTranslations } from "next-intl";
import DialogFooterWrapper from "../dialog-wrapper/dialog-footer.wrapper";
import { cn } from "@/lib/utils";
import useLocaleSwitcher from "@/hooks/use-locale-switcher";

interface LanguageSelectList {
  title: string,
  description?: string,
  id: string,
  value: string
}
export default function LocaleDialog() {
  const t = useTranslations()
  const { changeLanguage, locale } = useLocaleSwitcher()
  const [choosenLanguage, setChoosenLanguage] = useState(locale)
  const languages: LanguageSelectList[] = [
    {
      title: "English",
      value: "en",
      id: "1"
    },
    {
      title: "Bahasa Indonesia",
      value: "id",
      id: "2"
    }
  ]
  const [open, setOpen] = useState(false)
  return <DialogWrapper
    open={open}
    setOpen={setOpen}
    openTrigger={<Button size={"icon"} variant={"ghost"}>
      <Globe className="!w-6 !h-6" strokeWidth={1.5} />
    </Button>}
    dialogClassName="xl:min-w-[800px]"
  >
    <DialogHeaderWrapper>
      <h2 className="font-semibold">{t('settings.seekers.generalSetting.title')}</h2>
    </DialogHeaderWrapper>
    <div className="">
      <div>
        <div className="grid grid-cols-2 md:grid-cols-4 2xl:grid-cols-5 gap-4">
          {languages.map(item => <LanguageSelector
            key={item.id}
            setIsActive={() => changeLanguage(item.value)}
            isActive={item.value == locale.toLowerCase()}
            {...item} />)}
        </div>
      </div>
    </div>
    <DialogFooterWrapper>
      <Button variant={"ghost"}>
        {t('cta.cancel')}
      </Button>
      <Button variant={"default-seekers"}>
        {t('cta.save')}
      </Button>
    </DialogFooterWrapper>
  </DialogWrapper>
}


function LanguageSelector({
  title,
  description,
  isActive,
  setIsActive
}:
  {
    isActive?: boolean,
    setIsActive?: () => void
  } & LanguageSelectList) {
  return <div onClick={setIsActive} className={cn("cursor-pointer text-xs p-4 hover:bg-seekers-foreground rounded-xl", isActive ? "bg-seekers-primary hover:bg-seekers-primary-light text-white" : "text-neutral-950")}>
    <p className="font-semibold">{title}</p>
    <p>{description}</p>
  </div>
}