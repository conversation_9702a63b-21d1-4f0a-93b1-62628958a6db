import { useTranslations } from "next-intl"
import { useSubscriptionSignUpFormSchema } from "./use-subscription-signup-form.schema"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Form } from "@/components/ui/form"
import DefaultInput from "@/components/input-form/default-input"
import { Button } from "@/components/ui/button"
import { useSubscriptionSignUp } from "@/core/applications/mutations/subscription/use-subscription-sign-up"
import { PostSubscriptionSignUpDto } from "@/core/infrastructures/subscription/dto"

export default function SubscriptionSignUpForm({ priceId, productId, handleSubmit }: { productId: string, priceId: string, handleSubmit: (data: PostSubscriptionSignUpDto) => void }) {
  const t = useTranslations("seeker")
  const formSchema = useSubscriptionSignUpFormSchema()
  type formSchemaType = z.infer<typeof formSchema>
  const subscriptionMutation = useSubscriptionSignUp()
  const form = useForm<formSchemaType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      contact: "",
      firstName: "",
      lastName: ""
    }
  })

  async function onSubmit(value: formSchemaType) {
    const data: PostSubscriptionSignUpDto = {
      email: value.contact.trim(),
      price_id: priceId,
      first_name: value.firstName,
      last_name: value.lastName,
      product_id: productId
    }
    handleSubmit(data)
  }
  return <Form {...form}>
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      <section className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <DefaultInput
            form={form}
            label={t("form.label.firstName")}
            name="firstName"
            placeholder=""
            type="text"
            variant="float"
            labelClassName="text-xs text-seekers-text-light font-normal"
          />
          <DefaultInput
            form={form}
            label={t("form.label.lastName")}
            name="lastName"
            placeholder=""
            type="text"
            variant="float"
            labelClassName="text-xs text-seekers-text-light font-normal"
          />
        </div>
        <DefaultInput
          form={form}
          label={t("form.label.email")}
          name="contact"
          placeholder=""
          type="email"
          variant="float"
          labelClassName="text-xs text-seekers-text-light font-normal"
        />
      </section>
      <Button loading={subscriptionMutation.isPending} className="w-full !mt-8" variant={"default-seekers"}>
        {t("cta.signUp")}
      </Button>
    </form>
  </Form>
}