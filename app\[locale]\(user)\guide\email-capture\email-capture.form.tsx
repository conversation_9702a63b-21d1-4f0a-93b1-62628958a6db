"use client"
import { useToast } from "@/hooks/use-toast"
import { useTranslations } from "next-intl"
import { useEmailCaptureFormSchema } from "./use-email-capture-form.schema"
import { useJoinWaitingList } from "@/core/applications/mutations/waiting-list/use-join-waiting-list"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { WaitingListDto } from "@/core/infrastructures/waiting-list/dto"
import { gtagEvent } from "@/lib/utils"
import { Form } from "@/components/ui/form";
import DefaultInput from "@/components/input-form/default-input"
import CheckboxInput from "@/components/input-form/checkbox-input"
import { Button } from "@/components/ui/button"
import { Download } from "lucide-react"

export default function EmailCaptureForm({ setSuccess }: { setSuccess: (val: boolean) => void }) {
  const t = useTranslations("seeker")
  const { toast } = useToast()
  const formSchema = useEmailCaptureFormSchema()
  type formSchematype = z.infer<typeof formSchema>
  const useWaitingJoinMutation = useJoinWaitingList()
  const form = useForm<formSchematype>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      name: "",
      agreeForNewsletter: false
    }
  })
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    const data: WaitingListDto = {
      name: values.name,
      email: values.email
    }
    try {
      await useWaitingJoinMutation.mutateAsync(data)
      gtagEvent({
        action: "lead_magnet_form_submit",
        category: "Lead Magnet",
        label: "Email Capture",
        value: "1"
      })

      toast({
        title: t("success.checkYourEmail"),
        description: t("success.pdfDownload.description"),
      });
      setSuccess(true)
    } catch (e: any) {
      toast({
        title: t("error.subscibeToNewsLetter"),
        description: e.response.data.message,
        variant: "destructive",
      });
    } finally {

    }
  }
  return <Form {...form}>
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid md:grid-cols-2 gap-4">

        <DefaultInput
          type="text"
          form={form}
          name="name"
          variant="default"
          label={t("form.label.name")}
          placeholder=""
          inputProps={{
            required: true
          }}
        />
        <DefaultInput
          type="email"
          form={form}
          name="email"
          variant="default"
          label={t("form.label.email")}
          placeholder=""
          inputProps={{
            required: true
          }}
        />
      </div>
      <CheckboxInput
        form={form}
        name="agreeForNewsletter"
        label="I agree to receive helpful updates about Bali housing and Property Plaza.
                No spam, unsubscribe anytime."

      />

      <Button
        type="submit"
        disabled={useWaitingJoinMutation.isPending}
        size="lg"
        className="w-full bg-teal-600 hover:bg-teal-700 text-white px-8 py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
      >
        {useWaitingJoinMutation.isPending ? (
          <>
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
            {t("cta.sendingGuide")}
          </>
        ) : (
          <>
            <Download className="mr-2 h-5 w-5" />
            {t("cta.sendMeGuide")}
          </>
        )}
      </Button>

      <p className="text-center text-sm text-gray-500">
        {t("guide.emailCapture.footnote")}

      </p>
    </form>
  </Form>
}