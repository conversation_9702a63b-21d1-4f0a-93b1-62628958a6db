import { useTranslations } from "next-intl";
import FilterContentLayout from "./filter-content-layout";
import { BaseSelectInputValue } from "@/types/base";
import CheckboxFilterItem from "./checkbox-filter-item";
import { useSeekerFilterStore } from "@/stores/seekers-filter-2.store";
import Image from "next/image";
import BathupIcon from "@/components/icons/property-detail/Bathtub.svg"
import AirConditionIcon from "@/components/icons/property-detail/Air Conditioning.svg"
import PetAllowedIcon from "@/components/icons/property-detail/Pet allowed.svg"
import GardenBackyardIcon from "@/components/icons/property-detail/Garden-Backyard.svg"
import GazeboIcon from "@/components/icons/property-detail/Gazebo.svg"
import RooftopTerraceIcon from "@/components/icons/property-detail/Rooftop terrace.svg"
import BalconyIcon from "@/components/icons/property-detail/Balcony.svg"
import TerraceIcon from "@/components/icons/property-detail/Terrace.svg"
import { cn } from "@/lib/utils";
import { SellingPointList } from "@/core/domain/listing/listing-seekers";
export default function FeaturesFilter() {
  const t = useTranslations("seeker")
  const { features, setFeatures } = useSeekerFilterStore(state => state)
  const views: BaseSelectInputValue<string>[] = [
    {
      id: "1",
      content: <div className="flex gap-1 items-center">
        <Image
          src={BathupIcon}
          alt=""
          className={cn("w-4 h-4 invert", features.includes(SellingPointList.bathub) ? "invert" : "invert-0")}
          width={16}
          height={16}

        />
        <span className="">{t('listing.featureFilter.optionOne.title')}</span>
      </div>,
      value: SellingPointList.bathub
    },
    {
      id: "2",
      content: <div className="flex gap-1 items-center">
        <Image
          src={AirConditionIcon}
          alt=""
          className={cn("w-4 h-4 invert", features.includes("AIR_CONDITION") ? "invert" : "invert-0")}
          width={16}
          height={16} />
        <span className="">{t('listing.featureFilter.optionTwo.title')}</span>
      </div>,
      value: SellingPointList.airCondition
    },
    {
      id: "3",
      content: <div className="flex gap-1 items-center">
        <Image
          src={PetAllowedIcon}
          alt=""
          className={cn("w-4 h-4 invert", features.includes(SellingPointList.petAllowed) ? "invert" : "invert-0")}
          width={16}
          height={16}
        />
        <span className="">{t('listing.featureFilter.optionThree.title')}</span>
      </div>,
      value: SellingPointList.petAllowed
    },

    {
      id: "4",
      content: <div className="flex gap-1 items-center">
        <Image
          src={GardenBackyardIcon}
          alt=""
          className={cn("w-4 h-4 invert", features.includes(SellingPointList.garden) ? "invert" : "invert-0")}
          width={16}
          height={16} />
        <span className="">{t('listing.featureFilter.optionFour.title')}</span>
      </div>,
      value: SellingPointList.garden
    },
    {
      id: "5",
      content: <div className="flex gap-1 items-center">
        <Image
          src={GazeboIcon} alt=""
          className={cn("w-4 h-4 invert", features.includes(SellingPointList.gazebo) ? "invert" : "invert-0")}
          width={16}
          height={16} />
        <span className="">{t('listing.featureFilter.optionFive.title')}</span>
      </div>,
      value: SellingPointList.gazebo
    },
    {
      id: "6",
      content: <div className="flex gap-1 items-center">
        <Image
          src={RooftopTerraceIcon}
          alt=""
          className={cn("w-4 h-4 invert", features.includes(SellingPointList.rooftopTerrace) ? "invert" : "invert-0")}
          width={16}
          height={16}
        />
        <span className="">{t('listing.featureFilter.optionSix.title')}</span>
      </div>,
      value: SellingPointList.rooftopTerrace
    },
    {
      id: "7",
      content: <div className="flex gap-1 items-center">
        <Image
          src={BalconyIcon}
          alt="" className={cn("w-4 h-4 invert", features.includes(SellingPointList.balcony) ? "invert" : "invert-0")}
          width={16}
          height={16}
        />
        <span className="">{t('listing.featureFilter.optionSeven.title')}</span>
      </div>,
      value: SellingPointList.balcony
    },
    {
      id: "8",
      content: <div className="flex gap-1 items-center">
        <Image
          src={TerraceIcon} alt=""
          className={cn("w-4 h-4 invert", features.includes(SellingPointList.terrace) ? "invert" : "invert-0")}
          width={16}
          height={16}
        />
        <span className="">{t('listing.featureFilter.optionEight.title')}</span>
      </div>,
      value: SellingPointList.terrace
    },
  ]
  return <FilterContentLayout title={t('listing.featureFilter.title')}>
    <div className="flex flex-wrap gap-2">
      {
        views.map(item =>
          <CheckboxFilterItem
            key={item.id}
            item={item}
            setValue={setFeatures}
            isActive={features.includes(item.value)} />
        )}
    </div>
  </FilterContentLayout>
}