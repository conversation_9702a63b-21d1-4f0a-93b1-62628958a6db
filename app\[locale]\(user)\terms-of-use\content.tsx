import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { DefaultContent } from "@/core/services/sanity/types";
import { PortableText } from "next-sanity";

export default function Content({ content }: { content: DefaultContent }) {
  return <MainContentLayout>
    <article className="prose prose-big max-w-3xl text-seekers-text mb-4">
      <PortableText value={content.body}
        components={{
          block: {
            h1: ({ children }) => <h2 className="text-2xl font-semibold text-seekers-text mt-4">{children}</h2>,
            h2: ({ children }) => <h3 className="text-xl font-semibold mt-4">{children}</h3>,
            h3: ({ children }) => <h3 className="text-lg font-semibold mt-4">{children}</h3>,
            h4: ({ children }) => <h3 className="">{children}</h3>,
            normal: ({ children }) => <p className=" leading-relaxed mt-2">{children}</p>
          },
          list: {
            number: ({ children }) => <ol className="list-decimal list-inside mt-2">{children}</ol>,
            bullet: ({ children }) => <ul className="list-disc pl-4 mt-2">{children}</ul>
          }
        }} />
    </article>
  </MainContentLayout>
}