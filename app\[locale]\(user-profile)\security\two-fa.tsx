"use client"
import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Smartphone } from "lucide-react"
import { useTranslations } from "next-intl"
import { useState } from "react"
import TwoFADialog from "./two-fa-dialog"
import { useUserStore } from "@/stores/user.store"

export default function TwoFA() {
  const t = useTranslations("seeker")
  const { seekers } = useUserStore()
  return <Card>
    <CardHeader className="flex max-sm:flex-col max-sm:items-start max-sm:gap-4 max-sm:pb-4 flex-row items-center justify-between space-y-0 pb-2">
      <div className="space-y-1">
        <CardTitle className="text-seekers-primary flex items-center">
          <Smartphone className="mr-2 h-4 w-4" />
          {t("setting.profile.security.twoFA.title")}
        </CardTitle>
        <CardDescription>{t("setting.profile.security.twoFA.description")}</CardDescription>
      </div>
      <TwoFADialog enableTwoFA={seekers.has2FA} />
    </CardHeader>
    <CardContent>
      <div className="flex items-center space-x-2">
        <span className="text-sm text-muted-foreground">{t('misc.status')}</span>
        <Badge
          variant={seekers.has2FA ? "default" : "destructive"}
          className={cn(seekers.has2FA ? "bg-green-500/10 text-green-500" : "bg-red-500/10 text-red-500")}
        >
          {seekers.has2FA ? t("cta.enable") : t("cta.disable")}
        </Badge>
      </div>
    </CardContent>
  </Card>
}