"use client"
import { useState } from "react"
import { FieldValues } from "react-hook-form"
import { FormControl, FormDescription, FormField, FormLabel } from "../ui/form"
import { BaseInputForm } from "@/types/base"
import BaseInputLayout from "./base-input"
import { Checkbox } from "../ui/checkbox"


interface CheckboxInputProps<T extends FieldValues> extends BaseInputForm<T> {
  label: string,
  description?: string,
  children?: React.ReactNode,
  isEditable?: boolean,
}

export default function CheckboxInput<T extends FieldValues>({ form, label, name, isEditable, description }: CheckboxInputProps<T>) {
  const [disabled, setDisabled] = useState<boolean | undefined>(true)
  const handleDisabledState = (disabledStatus?: boolean, isEdited?: boolean) => {
    if (isEdited) {

    } else {
      form.reset()
    }
    setDisabled(disabledStatus)
  }
  return <FormField
    control={form.control}
    name={name}
    render={({ field }) => (
      <BaseInputLayout>
        <div className="flex gap-1">
          <FormControl>
            <Checkbox
              checked={field.value}
              onCheckedChange={field.onChange}
              disabled={field.disabled}
            />
          </FormControl>
          <div className="space-y-1 leading-none">
            <FormLabel>
              {label}
            </FormLabel>
            {
              description &&
              <FormDescription>
                {description}
              </FormDescription>
            }
          </div>
        </div>
      </BaseInputLayout>

    )}
  />
}