import {
  NextFetchEvent,
  NextMiddleware,
  NextRequest,
  NextResponse,
} from "next/server";
import { MiddlewareFactory } from "./types";
import { loginUrl, NEED_AUTHENTICATED_PAGE } from "@/lib/constanta/route";
import { ACCESS_TOKEN, USER } from "@/lib/constanta/constant";
import { removeLocaleFromUrl } from "@/lib/utils";

const seekerOrigin = [
  "https://www.property-plaza.id",
  "https://www.property-plaza.com",
  process.env.USER_DOMAIN,
];

export const withAuth: MiddlewareFactory =
  (next: NextMiddleware) =>
  async (request: NextRequest, _next: NextFetchEvent) => {
    let nextResult = await next(request, _next);
    let response = NextResponse.next();
    const cookiesToken = request.cookies.get(ACCESS_TOKEN);
    const hasToken = cookiesToken?.value ? true : false;
    const rawPathName = request.nextUrl.pathname;
    const pathName = removeLocaleFromUrl(rawPathName);

    const isProtected = NEED_AUTHENTICATED_PAGE.some((item) =>
      pathName.includes(item)
    );

    if (!hasToken && isProtected) {
      const login = new URL(loginUrl, request.url);
      const homepageSeeker = new URL("/", request.url);
      if (seekerOrigin.includes(request.nextUrl.origin))
        return NextResponse.redirect(homepageSeeker);
      return NextResponse.redirect(login);
    }

    if (nextResult) {
      return nextResult;
    }
    return response;
  };
