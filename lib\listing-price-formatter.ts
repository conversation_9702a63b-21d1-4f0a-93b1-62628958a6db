
import { formatDateSuffix, ListingContractType } from "@/core/domain/listing/listing";
import { ItemWithSuffix } from "@/core/domain/utils/utils";
import { getTranslations } from "next-intl/server";

export const seekersPriceHelper = async (price:number,contractType:ListingContractType,minDuration?:ItemWithSuffix,maxDuration?:ItemWithSuffix) => {
  const t = await getTranslations("seeker")
  let startWord = ""
  let suffix = ""
  let formattedPrice = price

  const minSuffix = formatDateSuffix(minDuration?.suffix || "")
  const maxSuffix = formatDateSuffix(maxDuration?.suffix || "")

  const handleSetWording = () => {
  switch(contractType){
    case "LEASEHOLD":
      const formattedMaxSuffix = maxSuffix == "MONTH" ? t('misc.month', {count:maxDuration?.value || 1}) : maxSuffix == "YEAR" ? t("misc.yearWithCount",{count:maxDuration?.value || 1}) : maxSuffix
      suffix = t('listing.pricing.suffix.leasehold',{count: maxDuration?.value || 1, durationType: formattedMaxSuffix})
      price = price 
      return 
    case "FREEHOLD":
      price = price 
      startWord =  t('conjuntion.for')
      return 
    case "RENT":
      price = price
      const formattedMinSuffix = minSuffix == "MONTH" ? t('misc.month', {count:1}) : minSuffix == "YEAR" ? t("misc.yearWithCount",{count:1}) : maxSuffix
      suffix = `/ ${formattedMinSuffix}`
      startWord = t('misc.startFrom')
      return 
    default:
      return ""
    }
  }
  handleSetWording()
  return {startWord,suffix, formattedPrice}
}