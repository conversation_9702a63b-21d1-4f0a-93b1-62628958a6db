import { BaseMetaDto } from "../utils/dto";

export interface RegisterUserDto {
  first_name: string;
  last_name: string;
  email?: string;
  phone_code?: string;
  phone_number?: string;
  password: string;
  confirm_password: string;
  type: "SEEKER" | "OWNER";
  otp: string;
  register_with?: string;
}

export interface RegisterUserResponse {
  access_token: string;
  expires_in: string;
}

export interface RegisterStaff {
  role_id: string;
  username: string;
  email: string;
  password: string;
  fullname: string;
  mobile_phone: string;
}

export interface MembershipDto {
  detail: {
    name: string;
    description: string;
  };
  start_at: string;
  end_at: string;
}

export interface AccountDto {
  first_name: string;
  last_name: string;
  is_subscriber: boolean;
  created_at: string;
  membership: MembershipDto | null;
}

export interface UserDto {
  code: string;
  email: string;
  phone_number: string;
  is_active: boolean;
  type: string;
  accounts: AccountDto;
}

export interface UserDetailDto {
  code: string;
  email: string;
  phone_number: string;
  phone_code: string;
  is_active: boolean;
  is_2fa: boolean;
  type: string;
  accounts: AccountDetailDto;
}
export interface AccountDetailDto {
  first_name: string;
  last_name: string;
  image: string;
  about: string;
  citizenship: string;
  language: string;
  facebook_social: string;
  twitter_social: string;
  is_subscriber: boolean;
  created_at: string;
  subscription: MembershipDto;
  credit: {
    amount: number;
    updated_at: string;
  };
  address: string;
  settings: SettingsUser;
}

export interface GetAllUserResponseData {
  items: UserDto[];
  meta: BaseMetaDto;
}

export interface SettingsUser {
  sound_notif?: boolean;
  message_notif?: boolean;
  property_notif?: boolean;
  price_alert_notif?: boolean;
  newsletter_notif?: boolean;
  special_offer_notif?: boolean;
  survey_notif?: boolean;
}

export interface UpdateUserDto {
  address?: string;
  language_interface?: string;
  language?: string;
  citizenship?: string;
  twitter_profile_url?: string;
  facebook_profile_url?: string;
  image?: string;
  about?: string;
  first_name?: string;
  last_name?: string;
  phone_code?: string;
  phone_number?: string;
  settings?: SettingsUser;
}
