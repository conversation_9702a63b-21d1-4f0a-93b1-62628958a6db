import {
  ADMIN,
  OWNER,
  PROPERTY_OWNER,
  PROPERTY_REPRESENTATIVE,
  PROPERTY_SEEKERS,
  USER,
} from "@/lib/constanta/constant";
import { Role } from "./base";

export type UserRole = typeof USER | typeof OWNER | typeof ADMIN;

export type SubscriptionStatus =
  | "not-active"
  | "active"
  | "expired"
  | "cancelled";
export interface UserData {
  id: string;
  name: string;
  location: string;
  subscriptionStatus: SubscriptionStatus;
}

export interface OwnerData {
  id: string;
  name: string;
  location: string;
  totalProperty: string;
}
export interface AdminData {
  id: string;
  name: string;
  role: Role;
}

export type UserType =
  | typeof PROPERTY_SEEKERS
  | typeof PROPERTY_OWNER
  | typeof PROPERTY_REPRESENTATIVE;
