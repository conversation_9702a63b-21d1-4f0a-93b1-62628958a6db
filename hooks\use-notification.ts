"use client"
import { useSettingStore } from "@/stores/setting.store"
import { useEffect, useState } from "react"

export const useNotification = () => {
  const { setNotificationSound} = useSettingStore(state => state)
  const [audio, setAudio] = useState<HTMLAudioElement | null>(null)

  useEffect(() => {
    const newAudio = new Audio()
    newAudio.src = "/sounds/notification.mp3"
    setAudio(newAudio)
  }, [])
  const playSound = () => {

    // if(!hasNotificationSound) return
    const newAudio = new Audio()
    newAudio.src = "/sounds/notification.mp3"
    newAudio.volume = 1
    newAudio.play().then(() => {
    }).catch(err => {
      console.error("sound error", err)
    })
  }
  const enableSoundNotification = (isEnable: boolean) => {
    setNotificationSound(isEnable)
  }
const popUpNotification = (title: string, description?: string) => {
  // Check if the Notification API is available in the current browser
  if (!("Notification" in window)) {
    console.warn("This browser does not support desktop notifications.");
    return;
  }

  if (Notification.permission === "granted") {
    new Notification(title, {
      body: description || '', // Default to empty string if description is undefined
    });
  }

  // If the user hasn't granted or denied permission, request it
  else if (Notification.permission === "default") {
    Notification.requestPermission().then((permission) => {
      if (permission === "granted") {
        new Notification(title, {
          body: description || '', // Default to empty string if description is undefined
        });
      } else {
        console.warn("Notification permission denied.");
      }
    });
  } 

  // If permission has been denied, log a warning or take another action
  else if (Notification.permission === "denied") {
    console.warn("Notifications are denied by the user.");
  }
};
  return {enableSoundNotification,playSound,popUpNotification}
}