import Compressor from "compressorjs"

interface ResultCompressImage {
  data: File | Blob | undefined,
  error: string
}
export async function compressImage(file:File):Promise<File | Error>{
  const result =  await new Promise((resolve,reject) => {
    new Compressor(file, {
    quality: 0.5,
    mimeType: "image/webp",
    success: async (response) => {
      resolve(response)
    },
    error: async (e) => {
      reject(e)
    }
  })
  })
  return result as File
}

export function downloadFile(file:File | Blob, filename:string){
  const link = document.createElement("a")
  const url = URL.createObjectURL(file)

  link.href= url
  link.download = filename
  link.click()

  URL.revokeObjectURL(url)

}