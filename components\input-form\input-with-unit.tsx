import { FormField } from '@/components/ui/form'
import { FieldValues } from 'react-hook-form'
import { Input } from '../ui/input'
import { BaseInputForm } from '@/types/base'
import BaseInputLayout from './base-input'

interface DefaultInputProps<T extends FieldValues> extends BaseInputForm<T> {
  label: string,
  placeholder: string
  description?: string
  type: string,
  unit: React.ReactNode
}

export default function InputWithUnit<T extends FieldValues>({ form, label, name, placeholder, description, type, unit }: DefaultInputProps<T>) {
  return <FormField
    control={form.control}
    name={name}
    render={({ field }) => (
      <BaseInputLayout label={label} description={description}>
        <div className='flex gap-2 w-full border rounded-sm focus-within:border-neutral-light'>
          <Input type={type} placeholder={placeholder} {...field} className='border-none focus:outline-none shadow-none focus-visible:ring-0' />
          <div className="min-w-[25%] border-l min-h-full flex items-center justify-center px-4 text-sm text-neutral">
            {unit}
          </div>
        </div>
      </BaseInputLayout>
    )}
  />
}