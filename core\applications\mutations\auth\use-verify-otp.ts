import {
  otpVerification,
  sendEmailverification,
} from "@/core/infrastructures/auth";
import {
  OtpVerificationDto,
  SendEmailVerificationDto,
} from "@/core/infrastructures/auth/dto";
import { useToast } from "@/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

export function useVerifyOtp(onSuccess: () => Promise<void>) {
  const { toast } = useToast();
  const t = useTranslations("universal");
  const mutation = useMutation({
    mutationFn: (data: OtpVerificationDto) => otpVerification(data),
    onSuccess: async (response) => {
      await onSuccess();
    },
    onError: (error) => {
      const data: any = (error as any).response.data;
      toast({
        title: t("misc.foundError"),
        description: data.message,
        variant: "destructive",
      });
      return data;
    },
  });
  return mutation;
}
