import { seekersListingFilterType } from "@/core/domain/listing/listing-seekers"
import { toggleStringArrrayItem } from "@/lib/utils"
import { create } from "zustand"

export interface RangeFilter {
  min: number, max: number
}
interface Filter {
  typeProperty: string,
  setTypeProperty: (val:string) => void,
  subTypeProperty: string[],
  setSubTypeProperty: (val:string) => void,
  priceRange: RangeFilter,
  setPriceRange: (min:number, max:number) => void,
  buildingSize: RangeFilter,
  setBuildingSize: (min:number, max:number) => void,
  landSize: RangeFilter,
  setLandSize: (min:number, max:number) => void,
  gardenSize: RangeFilter,
  setGardenSize: (min:number, max:number) => void,
  room:string, 
  setRoom:(val:string) => void,
  bedRoom:string,
  setBedroom:(val:string) => void,
  bathRoom:string,
  setBathRoom:(val:string) => void,
  rentalIncluding: string[],
  setRentalIncluding: (val:string | string) => void,
  location: string[],
  setLocation: (val:string | string) => void,
  features: string[],
  setFeatures: (val:string | string) => void,
  propertyCondition: string[],
  setPropertyCondition: (val:string | string) => void,
  elictricity: string,
  setElictricity: (val:string ) => void,
  typeLiving: string,
  setTypeLiving: (val:string) => void,
  view: string[],
  setView: (val:string) => void,
  typeContract: string,
  setTypeContract: (val:string) => void,
  minimumContract: string,
  setMinimumContract: (val:string) => void,
  yearsOfBuild: string,
  setYearsOfBuild: (val:string) => void,
  clearSubTypeProperty: () => void
}

export const defaultRangeFilter:RangeFilter = {
    min: 0,
    max: 50000000
}
export const defaultPropertySizeRangeFilter:RangeFilter = {
    min: 0,
    max: 10_000_000
}
export const useSeekerFilterStore = create<Filter>(set => ({
  bathRoom: "any",
  bedRoom: "any",
  buildingSize: defaultPropertySizeRangeFilter,
  elictricity: "",
  features: [],
  gardenSize: defaultPropertySizeRangeFilter,
  landSize: defaultPropertySizeRangeFilter,
  location: [],
minimumContract: "",
  priceRange: defaultRangeFilter,
  propertyCondition: [],
  rentalIncluding: [],
  room: "",
  typeContract: "",
  typeLiving: "",
  typeProperty: seekersListingFilterType.anything,
  view: [],
  yearsOfBuild: "",
  subTypeProperty: [],
  setSubTypeProperty: (subType: string) =>
    set((state) => ({
      subTypeProperty: toggleStringArrrayItem(state.subTypeProperty, subType),
  })),
  setBathRoom: (bathRoom: string) => set(() => ({ bathRoom })),
  setBedroom: (bedRoom: string) => set(() => ({ bedRoom })),
    setBuildingSize: (min: number, max: number) =>
    set((state) => ({
      buildingSize: { ...state.buildingSize, min, max },
    })),
  setElictricity: (elictricity) => set(() => ({elictricity})),
setFeatures: (val: string) =>
    set((state) => ({
      features: toggleStringArrrayItem(state.features, val),
    })),
  setGardenSize: (min: number, max: number) =>
    set((state) => ({
      gardenSize: { ...state.gardenSize, min, max },
    })),
  setLandSize: (min: number, max: number) =>
    set((state) => ({
      landSize: { ...state.landSize, min, max },
    })),
  setLocation: (val: string) =>
    set((state) => ({
      location: toggleStringArrrayItem(state.location, val),
    })),
  setMinimumContract: (minimumContract: string) =>
    set(() => ({ minimumContract })),
  setPriceRange: (min: number, max: number) =>
    set((state) => ({
      priceRange: { ...state.priceRange, min, max },
    })),
  setPropertyCondition: (val: string) =>
    set((state) => ({
      propertyCondition: toggleStringArrrayItem(state.propertyCondition, val),
    })),
  setRentalIncluding: (val: string) =>
    set((state) => ({
      rentalIncluding: toggleStringArrrayItem(state.rentalIncluding, val),
    })),
  setRoom: (room: string) => set(() => ({ room })),
  setTypeContract: (typeContract: string) => set(() => ({ typeContract })),
  setTypeLiving: (typeLiving: string) => set(() => ({ typeLiving })),
  setTypeProperty: (typeProperty: string) => set(() => ({ typeProperty })),
  setView: (val: string) =>
    set((state) => ({ view: toggleStringArrrayItem(state.view, val) })),
  setYearsOfBuild: (yearsOfBuild: string) => set(() => ({ yearsOfBuild })),
  clearSubTypeProperty: () => set(() => ({ subTypeProperty: [] }))
})
)

