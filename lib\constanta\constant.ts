import { User } from "@/types/base";

export const locales = ["id", "en"];
export const defaultLocale = "en";
export const userRole: User = "user";

export const stepperParamKey = "step";
export const StepperItemId = {
  location: "1",
  basicInfo: "2",
  pricing: "3",
  features: "4",
  images: "5",
};

export const CREATE_LISTING = "create";
export const UPDATE_LISTING = "update";
export const REVIEW = "review";
export const REVISI = "Revision";

export const USER = "User";
export const OWNER = "Owner";
export const ADMIN = "Admin";
export const ACCESS_TOKEN = "tkn";
export const ACCESS_TOKEN_SEEKERS = "tkn";

export const PROPERTY_SEEKERS = "SEEKER";
export const PROPERTY_OWNER = "OWNER";
export const PROPERTY_REPRESENTATIVE = "MIDDLEMAN";

export const WHATSAPP = "whatsapp";
export const EMAIl = "email";
export const VIBER = "viber";

export const WAITING_LIST_MUTATION_ID = "waiting-list-mutation-id";

export const PASSWORD_MIN_LENGTH = 8;

export const MIN_ADDRESS_LENGTH = 3;
export const MAXIMUM_ADDRESS_LENGTH = 50;
export const MIN_NAME_LENGTH = 1;
export const MAX_NAME_LENGTH = 30;

export const MAX_MESSAGE_COUNT = 300;
export const MIN_MESSAGE_COUNT = 10;

export const COOKIES_COLLECTION_STATUS = "cookies-collection-status";
export const NECESSARY_COOKIES_COLLECTION =
  "necessary-cookies-collection-status";
export const FUNCTIONAL_COOKIES_COLLECTION =
  "functional-cookies-collection-status";
export const ANALYTIC_COOKIES_COLLECTION = "analytic-cookies-collection-status";
export const MARKETING_COOKIES_COLLECTION =
  "marketing-cookies-collection-status";

export const filterTitles = {
  type: "t",
  minPrice: "minp",
  maxPrice: "maxp",
  landLargest: "landl",
  landSmallest: "lands",
  buildingLargest: "buildl",
  buildingSmallest: "builds",
  gardenLargest: "gardenl",
  gardenSmallest: "gardens",
  yearsOfBuild: "yob",
  bedroomTotal: "bedt",
  bathroomTotal: "batht",
  rentalOffer: "ro",
  propertyCondition: "pc",
  electircity: "el",
  parking: "pk",
  swimmingPool: "sp",
  typeLiving: "tl",
  furnished: "fs",
  view: "v",
  minimumContract: "minc",
  category: "c",
  subCategory: "sc",
  feature: "feat",
  propertyLocation: "pl",
  zoom: "z",
  viewMode: "viewMode",
};
