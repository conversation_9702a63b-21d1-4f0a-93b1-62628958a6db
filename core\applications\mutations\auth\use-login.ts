import { useMutation } from "@tanstack/react-query";
import { login } from "@/core/infrastructures/auth";
import { LoginDto } from "@/core/infrastructures/auth/dto";
import { useToast } from "@/hooks/use-toast";
import { ACCESS_TOKEN } from "@/lib/constanta/constant";
import Cookies from "js-cookie";
import { useTranslations } from "next-intl";
import { accountMiddlemanUrl, accountUrl } from "@/lib/constanta/route";
import { getMeService } from "@/core/infrastructures/user/services";
import { useReCaptcha } from "next-recaptcha-v3";

export function useLogin(
  typeUser: "seekers" | "owner" | "middleman" = "seekers"
) {
  const t = useTranslations("universal");
  const { toast } = useToast();
  const { executeRecaptcha } = useReCaptcha();
  const mutation = useMutation({
    mutationFn: async (data: LoginDto) => {
      try {
        const token = await executeRecaptcha("form_submit");
        return login(data, token);
      } catch (e: unknown) {
        console.log(e);
        return {
          data: null,
        };
      }
    },
    onSuccess: async (response) => {
      const data = response.data;
      const users = await getMeService({
        headers: {
          Authorization: `Bearer ${data.data.access_token}`,
        },
      });
      const userType = users.type;
      if (!users) throw new Error(t("misc.userNotFound"));
      if (typeUser === "owner" || typeUser == "middleman") {
        if (userType == "SEEKER") throw new Error(t("misc.userNotFound"));
        Cookies.set(ACCESS_TOKEN, data.data.access_token, { expires: 7 });
        if (userType == "OWNER") {
          return window.location.assign(accountUrl);
        } else if (userType == "MIDDLEMAN") {
          return window.location.assign(accountMiddlemanUrl);
        }
      } else {
        if (users.type == "OWNER" || (users.type as any) == "MIDDLEMAN")
          throw new Error(t("misc.userNotFound"));
        Cookies.set(ACCESS_TOKEN, data.data.access_token, { expires: 7 });
        window.location.reload();
      }
    },
    onError: (error) => {
      const data: any = (error as any).response?.data;
      Cookies.remove(ACCESS_TOKEN);
      toast({
        title: t("misc.foundError"),
        description: data?.message || "",
        variant: "destructive",
      });
    },
  });
  return mutation;
}
