import { getDetailTransactionService } from "@/core/infrastructures/transaction/services"
import { useQuery } from "@tanstack/react-query"

export const GET_TRANSACTION_DETAIL_QUERY_KEY = "transaction-detail"
export function useGetTransactionDetail(id:string){
  const query = useQuery({
    queryKey: [GET_TRANSACTION_DETAIL_QUERY_KEY,id],
    queryFn: async () => await getDetailTransactionService(id),
    enabled:false
  })
  return query
}