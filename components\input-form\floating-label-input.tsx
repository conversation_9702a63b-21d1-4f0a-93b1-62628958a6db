"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { InputProps } from "../ui/input"

export const grayTextColor = "text-gray-500"

interface FloatingLabelInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string
  eyeColor?: string
}



export const FloatingLabelInput = React.forwardRef<
  HTMLInputElement,
  InputProps & { label: string }
>(({ className, type, label, ...props }, ref) => {
  return (
    <div className="relative">
      <label className="absolute -top-2 left-2 px-1 text-xs text-gray-500 bg-white z-10">
        {label}
      </label>
      <input
        type={type}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          "pt-4", // Extra padding voor label
          className
        )}
        ref={ref}
        {...props}
      />
    </div>
  );
});
FloatingLabelInput.displayName = "FloatingLabelInput" 