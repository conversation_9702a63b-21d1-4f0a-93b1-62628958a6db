import { apiClient } from "@/core/client";
import { GetChatsDto, NewChatDto } from "./dto";

export const postNewChats = (data:NewChatDto) => apiClient.post("room-chats",data)
export const getChats = (data:GetChatsDto) => apiClient.get(`room-chats?search=${data.search}`)

export const getChatRoomDetail = (id:string) => apiClient.get(`room-chats/${id}`)

export const updateMessageStatus = (id:string) => apiClient.put(`room-chats/${id}`)