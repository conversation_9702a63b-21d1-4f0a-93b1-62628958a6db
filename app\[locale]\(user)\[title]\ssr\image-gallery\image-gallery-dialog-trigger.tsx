"use client"
import { useFavoriteListing } from "@/hooks/use-post-favorite-listing";
import ImageGalleryDialog from "./image-gallery-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { ImageIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import useImageGallery from "../utils/use-image-gallery";

export default function ImageGalleryDialogTrigger({ imageUrls }: { imageUrls: string[] }) {
  const t = useTranslations("seeker")
  const { authenticated: isAuthenticated, membership } = useFavoriteListing("")
  const { handleOpenAuthDialog, handleOpenSubscriptionDialog } = useImageGallery()
  return <>
    {!isAuthenticated ?
      <Button
        variant={"ghost"}
        onClick={handleOpenAuthDialog}
        className="absolute bottom-4 right-4 z-30 bg-white text-seekers-text-light font-medium gap-3">
        <ImageIcon className="!w-6 !h-6" />
        {t('listing.detail.images.showAllImages')}
      </Button>
      :
      // isAuthenticated && membership == membershipType.free ?
      //   <Button
      //     variant={"ghost"}
      //     onClick={handleOpenSubscriptionDialog}
      //     className="absolute bottom-4 right-4 z-10 bg-white text-seekers-text-light font-medium gap-3">
      //     <ImageIcon className="!w-6 !h-6" />
      //     {t('listing.detail.images.showAllImages')}
      //   </Button>
      //   :
      <ImageGalleryDialog
        imagesUrl={imageUrls} />
    }
  </>
}