import { packages, PackagesType } from "../subscription/subscription";

type OwnerType = "OWNER";
type SeekersType = "SEEKER";
type MiddlemanType = "MIDDLEMAN";
export type UserType = OwnerType | SeekersType | MiddlemanType;

export interface Account {
  firstName: string;
  lastName: string;
  isSubscriber: boolean;
  createdAt: string;
  membership: PackagesType;
}
export type ZoomFeature = {
  max: number;
  min: number;
};

export interface User {
  code: string;
  email: string;
  phoneNumber: string;
  isActive: boolean;
  type: UserType;
  accounts: Account;
}

export interface UserDetail {
  code: string;
  email: string;
  phoneNumber: string;
  phoneCode: string;
  isActive: boolean;
  type: UserType;
  accounts: AccountDetail;
  setting: UserSetting;
  has2FA: boolean;
}

export interface UserSetting {
  soundNotif?: boolean;
  messageNotif?: boolean;
  propertyNotif?: boolean;
  priceAlertNotif?: boolean;
  newsletterNotif?: boolean;
  specialOfferNotif?: boolean;
  surveyNotif?: boolean;
}
export interface AccountDetail {
  firstName: string;
  lastName: string;
  image: string;
  about: string;
  citizenship: string;
  language: string;
  facebookSocial: string;
  twitterSocial: string;
  isSubscriber: boolean;
  membership: PackagesType;
  credit: {
    amount: number;
    updatedAt: string;
  };
  address: string;
  chat: {
    max: number;
    current: number;
  };
  zoomFeature: ZoomFeature;
}

export const checkIfValidSession = (token: string) => {
  return false;
};

export const hasSubscription = (isSubscribe: boolean) => {
  return isSubscribe;
};

export function membershipTypeFormatter(type: string): PackagesType {
  if (packages.free.includes(type)) return packages.free;

  if (packages.finder.includes(type)) return packages.finder;

  if (packages.archiver.includes(type)) return packages.archiver;

  return packages.free;
}

export function membershipMaximumChatBasedOnSubscription(type: PackagesType) {
  if (type == packages.free) {
    return 0;
  }
  if (type == packages.finder) {
    return 5;
  }
  if (type == packages.archiver) {
    return 10;
  }
  return 0;
}

export const minZoom = 10;
export const defaultZoomFeature: ZoomFeature = {
  max: 13,
  min: minZoom,
};
export function membershipZoomFeatureBasedOnSubscription(
  type: PackagesType
): ZoomFeature {
  if (type == packages.free) {
    return defaultZoomFeature;
  }
  if (type == packages.finder) {
    return {
      max: 14,
      min: minZoom,
    };
  }
  if (type == packages.archiver) {
    return {
      max: 15,
      min: minZoom,
    };
  }
  return defaultZoomFeature;
}
