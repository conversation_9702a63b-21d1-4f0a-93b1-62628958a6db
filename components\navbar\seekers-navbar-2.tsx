"use client"
import useSeekersSearch from "@/hooks/use-seekers-search";
import MainContentLayout from "../seekers-content-layout/main-content-layout";
import Image from "next/image";
import Logo from "@/public/property-seekers-main-logo.png"
import { Button } from "../ui/button";
import { Menu, Search, } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { AnimatePresence } from "framer-motion"
import { useSeekerSearchStore } from "@/stores/seeker-search.store";
import { cn } from "@/lib/utils";
import { Link } from "@/lib/locale/navigations";
import * as m from "framer-motion/m"
import dynamic from "next/dynamic";
import { LazyMotion, domAnimation } from "framer-motion"

const SeekersRightNavbar = dynamic(() => import("./seekers-right-navbar-2"), { ssr: false })
const LocationSearch = dynamic(() => import("./location-search/location-search-form"), { ssr: false })
const SeekerSearchDialog = dynamic(() => import("./seeker-search-dialog"), { ssr: false })
const TypeSearch = dynamic(() => import("./category-search/category-search-form"), { ssr: false })
export default function SeekersNavbar({ localeId = "EN", currency_ = "EUR" }: { localeId?: string, currency_?: string }) {

  const { handleSearch } = useSeekersSearch()
  const [openMobileNavbar, setOpenMobileNavbar] = useState(false) //for handling mobile navbar
  const mobileRef = useRef<HTMLDivElement | null>(null)
  const navbarRef = useRef<HTMLDivElement | null>(null)
  const { isOpen, setIsOpen, categoryInputFocused, locationInputFocused } = useSeekerSearchStore(state => state)

  // Useffect used to Handling the size of navbar, if scroll reach certain threshold, the search navbar will be smaller
  useEffect(() => {
    const SCROLL_THRESHOLD = 200
    const handleScrollY = () => {
      setOpenMobileNavbar(false)
      if (window.scrollY < SCROLL_THRESHOLD) {
        setIsOpen(true)
        return
      } else if (window.scrollY > SCROLL_THRESHOLD && (categoryInputFocused || locationInputFocused)) {
        setIsOpen(true)
      } else {
        setIsOpen(false)
      }
    }
    window.addEventListener("scroll", handleScrollY)
    return () => {
      window.removeEventListener("scroll", handleScrollY)
    }
  }, [categoryInputFocused, isOpen, locationInputFocused, setIsOpen])

  useEffect(() => {
    if (categoryInputFocused || locationInputFocused) {
      setIsOpen(true)
      return
    }
  }, [categoryInputFocused, locationInputFocused, setIsOpen])

  return <LazyMotion features={domAnimation}>
    <AnimatePresence>
      <nav ref={navbarRef} className="w-full max-xl:space-y-4 border-b shadow-sm shadow-neutral-600/20 bg-white md:h-[90px] lg:h-[114px]">
        <MainContentLayout className="!h-full relative py-4 max-lg:space-y-4 xl:py-6 space-y-8 ">
          <div className="w-full flex justify-between items-center flex-wrap gap-y-6">
            <Link href={"/"} hrefLang={localeId}>
              <Image src={Logo} priority alt="Property-Plaza" width={164} height={24} />
            </Link>
            <m.div
              className="flex gap-2 rounded-full p-2 border border-seekers-text-lighter shadow-md items-center max-lg:hidden pl-4"
              initial={{ opacity: 1, width: "60%" }}
              animate={{
                width: !isOpen ? "30%" : "60%",
              }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex flex-grow items-center overflow-hidden divide-x-2 divide-seekers-text-lighter">
                <div className="flex-grow min-w-[49%] max-w-[50%] pr-8">
                  <LocationSearch />
                </div>
                <div className="flex-grow min-w-[49%] max-w-[50%] pl-8">
                  <TypeSearch />
                </div>
              </div>
              <m.div
                initial={{
                  height: 48,
                  width: 48
                }}
                animate={{
                  height: !isOpen ? 36 : 48,
                  width: !isOpen ? 36 : 48
                }}
                transition={{ duration: 0.3 }}

              >
                <Button variant={"default-seekers"} onClick={() => handleSearch()} className="rounded-full w-full h-full !aspect-square" size={"icon"}>
                  <Search className="!w-5 !h-5" strokeWidth={3} />
                </Button>
              </m.div>
            </m.div>
            <div className="lg:hidden max-sm:w-full md:max-lg:w-[50%] max-sm:order-last flex gap-2">
              <SeekerSearchDialog />
            </div>
            <div className="md:hidden flex gap-1 w-[164px] justify-end">
              <Button variant={"ghost"} className="px-0 pl-4" onClick={() => setOpenMobileNavbar(prev => !prev)}>
                <Menu className="!h-6 !w-6" />
              </Button>
            </div>
            <div className="max-md:hidden flex gap-2 items-center w-fit justify-end min-w-[136px]">
              <SeekersRightNavbar currency_={currency_} localeId={localeId} />
            </div>

          </div>
        </MainContentLayout>
        <div className={cn(openMobileNavbar ? "fixed w-screen h-full bg-seekers-text/30 top-0 left-0 -z-10 !mt-0" : "hidden")}>
        </div>
        <div ref={mobileRef} className={`absolute top-12 z-30 bg-background left-0 w-full flex gap-2 items-center justify-end  ${openMobileNavbar ? "h-fit  py-4 px-4" : "h-0"} overflow-hidden transition-all ease-linear duration-75 transform`}>
          <SeekersRightNavbar currency_={currency_} localeId={localeId} />
        </div>
      </nav>
    </AnimatePresence>
  </LazyMotion >
}


