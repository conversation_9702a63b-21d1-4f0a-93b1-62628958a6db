"use client"
import useGet<PERSON>hatList from "@/core/applications/queries/messages/use-get-chat-list"
import useSearchParamWrapper from "@/hooks/use-search-param-wrapper"
import { useMessagingStore } from "@/stores/messaging.store"
import { useTranslations } from "next-intl"
import { useEffect } from "react"
import SearchAndFilterChat from "./search-and-filter-chat"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton"
import { MessageItem } from "@/core/domain/messages/messages"
import ChatItemPreview from "./chat-item-preview"

export default function ChatList() {
  const t = useTranslations("seeker")
  const { currentLayout, allChat, setAllChat, setRoomId } = useMessagingStore()
  const { searchParams } = useSearchParamWrapper()
  const search = searchParams.get("search") || ""
  const chatList = useGetChatList({ search: search })
  useEffect(() => {

    if (!chatList.data?.data) return
    const filter = searchParams.get("filter") || ""
    const data = chatList.data?.data
    if (data.length < 1) {
      setRoomId("")
    }
    if (filter) {
      const filteredData = data.filter(item => {

        const isRelatedFilter = !filter ? true : !filter?.includes(item.category)
        return isRelatedFilter
      })
      setAllChat(filteredData)
    } else {
      setAllChat(data)
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chatList.data?.data, searchParams.get("search"), searchParams.get("filter")])
  return <div className={`space-y-8 max-sm:space-y-4 md:max-w-xs flex flex-col max-sm:h-fit md:overflow-hidden ${currentLayout == 'list' ? "" : "max-lg:hidden"} min-w-[300px] md:max-lg:min-w-full max-sm:w-full`}>
    <SearchAndFilterChat />
    <ScrollArea className="overflow-y-auto md:flex-grow">
      {
        chatList.isPending ? <div className="w-full space-y-2">
          {Array(3).fill(0).map((_, idx) =>

            <Skeleton key={idx} className="w-full h-9" />
          )
          }
        </div>
          : <>
            {allChat.length < 1 ? <p className="text-center text-seekers-text-light">{t('message.notMessageList')}</p>
              :
              allChat.map((item: MessageItem, idx) => <ChatItemPreview key={idx} {...item} />)
            }
          </>
      }
      {/* only placeholder for bottom margin, please don't remove it! */}
      <div className="md:hidden h-20"></div>
    </ScrollArea>
    {/* <div className="space-y-2 max-sm:hidden">
      <StartChatWithCS />
    </div> */}
  </div>
}