import { useTranslations } from "next-intl";
import Image from "next/image";
import { ArrowDown, Download } from "lucide-react";
import CtaGuideHero from "./cta-guide-hero";
import GuideBackground from "@/public/guide-background.webp"
import { imagePlaceholder } from "@/lib/constanta/image-placeholder";

export default function GuideHeroSection() {
  const t = useTranslations("seeker");
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden pt-[114px]">
      {/* Background Image */}
      <Image
        src={GuideBackground}
        alt="Beautiful Bali villa with rice field view"
        fill
        className="object-cover brightness-50"
        priority
        sizes="100vw"
        quality={85}
        placeholder="blur"
        blurDataURL={imagePlaceholder}
      />

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8  text-white">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center min-h-screen py-20 lg:py-0">
          {/* Left Column - Text Content */}
          <div className="space-y-6 lg:space-y-8 order-2 lg:order-1">
            <div className="space-y-4 lg:space-y-6 text-center">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                <span>
                  {t('guide.hero.title')}
                </span>
                <span className="text-red-400 leading-tight">{t('guide.hero.titleHighlight')}</span>
              </h1>

              <p className="text-base sm:text-xl lg:text-2xl text-gray-200 leading-relaxed max-w-2xl mx-auto lg:mx-0">
                {t('guide.hero.subtitle')}
              </p>
            </div>

            <CtaGuideHero />

            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center lg:justify-start gap-6 text-sm text-gray-300">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>{t('guide.hero.trustIndicators.free')}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>{t('guide.hero.trustIndicators.noSpam')}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>{t('guide.hero.trustIndicators.instant')}</span>
              </div>
            </div>
          </div>

          {/* Right Column - Guide Mockup */}
          <div className="flex justify-center lg:justify-end order-1 lg:order-2 ">
            <div className="relative">
              {/* iPad/Phone Mockup */}
              <div className="relative w-72 sm:w-80 h-80 sm:h-96 rounded-2xl shadow-2xl p-3 sm:p-4 transform rotate-3 hover:rotate-0 transition-transform duration-500 bg-[#FBFAF7]">
                <div className="w-full h-full bg-gradient-to-br from-seekers-primary-lightest to-neutral-lightest rounded-xl flex flex-col items-center justify-center p-4 sm:p-6">
                  <div className="w-12 sm:w-16 h-12 sm:h-16 bg-seekers-primary rounded-full flex items-center justify-center mb-3 sm:mb-4">
                    <Download className="h-6 sm:h-8 w-6 sm:w-8 text-white" />
                  </div>
                  <h3 className="text-gray-800 font-bold text-base sm:text-lg text-center mb-2">
                    {t('guide.pdfPreview.title')}
                  </h3>
                  <p className="text-gray-600 text-xs sm:text-sm text-center mb-3 sm:mb-4 px-2">
                    {t('guide.pdfPreview.description')}
                  </p>
                  <div className="w-full h-24 sm:h-32 bg-white rounded-lg shadow-inner flex items-center justify-center">
                    <span className="text-gray-400 text-xs">{t('misc.pdfPreview')}</span>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-2 sm:-top-4 -right-2 sm:-right-4 w-10 sm:w-12 h-10 sm:h-12 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg animate-bounce">
                <span className="text-xs font-bold text-stone-800 uppercase">{t('misc.free')}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <ArrowDown className="h-6 w-6 text-white/70 absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce" />
    </section>
  );
}
