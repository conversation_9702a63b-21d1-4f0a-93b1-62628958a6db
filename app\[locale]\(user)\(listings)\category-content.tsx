"use client"
import { Coffee, DollarSign, Hotel, LandPlot, LucideHome, Palmtree, Presentation, School, ShoppingCart } from "lucide-react";
import DefaultLayoutContent from "../../../../components/seekers-content-layout/default-layout-content";
import CategoryItem, { CategoryItemProps } from "./category-item";
import { useTranslations } from "next-intl";
import { listingCategory } from "@/core/domain/listing/listing-seekers";
import { useRouter } from "nextjs-toploader/app";
import { filterTitles } from "@/lib/constanta/constant";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Badge } from "@/components/ui/badge";

export default function CategoryContent() {
  const t = useTranslations("seeker")
  const router = useRouter()
  const categoryDummy: CategoryItemProps[] = [
    {
      icon: <Palmtree className="!w-6 !h-6" />,
      title: t('listing.category.villa'),
      slug: "villas",
      value: listingCategory.villas
    },
    {
      icon: <Hotel className="!w-6 !h-6" />,
      title: t('listing.category.apartment'),
      slug: "apartments",
      value: listingCategory.apartment
    },
    {
      icon: <LucideHome className="!w-6 !h-6" />,
      title: t('listing.category.guestHouse'),
      slug: "rooms",
      value: listingCategory.rooms
    },
    {
      icon: <DollarSign className="!w-6 !h-6" />,
      title: t('listing.category.commercial'),
      slug: "commercial-spaces",
      value: listingCategory.commercialSpace
    },
    {
      icon: <Coffee className="!w-6 !h-6" />,
      title: t('listing.category.cafeAndRestaurent'),
      slug: "cafe-or-restaurents",
      value: listingCategory.cafeOrRestaurants
    },
    {
      icon: <Presentation className="!w-6 !h-6" />,
      title: t('listing.category.office'),
      slug: "offices",
      value: listingCategory.offices
    },
    {
      icon: <ShoppingCart className="!w-6 !h-6" />,
      title: t('listing.category.shops'),
      slug: "shops",
      value: listingCategory.shops
    },
    {
      icon: <School className="!w-6 !h-6" />,
      title: t('listing.category.shellAndCore'),
      slug: "shell-and-cores",
      value: listingCategory.shellAndCore
    },
    {
      icon: <LandPlot className="!w-6 !h-6" />,
      title: t('listing.category.land'),
      slug: "lands",
      value: listingCategory.lands
    },
  ]
  return <MainContentLayout>
    <DefaultLayoutContent
      title={t('listing.category.title')}
      action={{
        title: t('listing.viewAllProperties'),
        action: () => {
          router.push("/s/all?" + filterTitles.type + "=all")
        },
      }}
      className="!mt-2"
    >
      <div className="grid grid-cols-3 md:grid-cols-5 xl:grid-cols-9 gap-x-0 gap-y-2">
        {
          categoryDummy.map((item, idx) => <CategoryItem key={idx} {...item} />)
        }
      </div>
    </DefaultLayoutContent>
  </MainContentLayout>
}