import { BaseSelectInputValue } from "@/types/base"

export interface Role {
  id: string,
  name: string,
  description: null,
  isActive: boolean,
  totalStaff: number
}

export function setSelectableRole(rawData:Role[]){
  const data = rawData.filter(item => item.isActive)
  const selectableData:BaseSelectInputValue<string>[] = data.map(item => ({
    id: item.id,
    content: item.name,
    value: item.id
  })) 
  return selectableData
}