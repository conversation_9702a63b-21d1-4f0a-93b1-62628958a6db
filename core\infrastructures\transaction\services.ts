import { BaseMetaDto } from "../utils/dto";
import { errorHandling } from "../utils/error-handling";
import { transformMeta } from "../utils/transform";
import {
  getAllPackages,
  getAllTransaction,
  getDegtailTransaction,
  getPackageDetail,
  ssrGetPaymentMethod,
} from "./api";
import {
  GetAllTransactionDto,
  PaymentMethodDto,
  TransactionDto,
  TransactionSeekerDto,
} from "./dto";
import {
  transformCardBillingInfo,
  transformTopUp,
  transformTopUpDetail,
  transformTransactionDetail,
  transformTransactionSeeker,
  transfromPaymentMethod,
} from "./transform";

export async function getAllTopUpService() {
  try {
    const result = await getAllPackages();
    const data = result.data.data;
    return {
      data: transformTopUp(data),
      meta: undefined,
    };
  } catch (e: any) {
    const error = errorHandling(e);
    return { error };
  }
}
export async function getTopUpDetailService(id: string) {
  try {
    const result = await getPackageDetail(id);
    const data = result.data.data;
    return {
      data: transformTopUpDetail(data),
      meta: undefined,
    };
  } catch (e: any) {
    const error = errorHandling(e);
    return { error };
  }
}
export async function getAllSeekerTransactionService(
  searchParams: GetAllTransactionDto
) {
  try {
    const result = await getAllTransaction(searchParams);
    const data = result.data.data.items as TransactionSeekerDto[];
    const meta = result.data.data.meta as BaseMetaDto;
    return {
      data: transformTransactionSeeker(data),
      meta: transformMeta(meta),
    };
  } catch (e: any) {
    console.log(e);
    const error = errorHandling(e);
    return { error };
  }
}

export async function getDetailTransactionService(id: string) {
  try {
    const result = await getDegtailTransaction(id);
    const data = result.data.data;
    return {
      data: transformTransactionDetail(data),
      meta: undefined,
    };
  } catch (e: any) {
    const error = errorHandling(e);
    return { error };
  }
}

export async function getPaymentMethodService() {
  try {
    const result = await ssrGetPaymentMethod<PaymentMethodDto[]>();
    const data = result.data;
    if (data == null) return { data: [] };
    return {
      data: transfromPaymentMethod(data),
      billingInfo: transformCardBillingInfo(data[0]),
    };
  } catch (e: any) {
    const error = errorHandling(e);
  }
}
