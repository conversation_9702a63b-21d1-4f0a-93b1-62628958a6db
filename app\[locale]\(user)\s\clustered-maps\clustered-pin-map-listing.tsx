import { AdvancedMarkerAnchorPoint, InfoWindow, <PERSON><PERSON> as MapMarker, useMap } from '@vis.gl/react-google-maps';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { type Marker, MarkerClusterer } from '@googlemaps/markerclusterer';
import { ListingListSeekers } from '@/core/domain/listing/listing-seekers';
import { ClusteredPinMap } from './clustered-pin-map';
import { ListingImage, ListingPrice, ListingSellingPoint, ListingTitle, ListingWrapper } from '../../(listings)/listing-item';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import { useSeekersSearchMapUtil } from '@/stores/seekers-search-map-utils';

export type ClusteredTreeMarkersProps = {
  data: ListingListSeekers[];
  conversions: { [key: string]: number }
};

/**
 * The ClusteredTreeMarkers component is responsible for integrating the
 * markers with the markerclusterer.
 */
export const ClusteredPinMapListing = ({ data, conversions }: ClusteredTreeMarkersProps) => {
  const [markers, setMarkers] = useState<{ [key: string]: Marker }>({});
  const { focusedListing, setFocusedListing } = useSeekersSearchMapUtil()
  const viewMode = useSeekersSearchMapUtil(state => state.viewMode)
  const [listingOverview, setListingOverview] = useState<ListingListSeekers | null>()

  // create the markerClusterer once the map is available and update it when
  // the markers are changed
  const map = useMap();
  const clusterer = useMemo(() => {
    if (!map) return null;
    if (viewMode === "list") {
      return null
    }
    return new MarkerClusterer({
      map,
      renderer: {
        render: (cluster) => {
          const clusterDiv = document.createElement('div');
          clusterDiv.style.width = '36px';
          clusterDiv.style.height = '36px';
          clusterDiv.style.backgroundColor = "#B48B55";
          clusterDiv.style.borderRadius = '50%';
          clusterDiv.style.display = 'flex';
          clusterDiv.style.alignItems = 'center';
          clusterDiv.style.justifyContent = 'center';
          clusterDiv.style.color = '#FFFFFF';
          clusterDiv.style.fontWeight = 'bold';
          clusterDiv.style.fontSize = '14px';

          clusterDiv.textContent = cluster.count.toString();
          return new google.maps.marker.AdvancedMarkerElement({
            position: cluster.position,
            content: clusterDiv
          })
        }
      }
    });
  }, [map, viewMode]);

  useEffect(() => {
    if (!clusterer) return;
    clusterer?.clearMarkers();
    clusterer.addMarkers(Object.values(markers));
    return () => {
      clusterer.removeMarkers(Object.values(markers))
    }
  }, [clusterer, markers, viewMode]);

  // this callback will effectively get passsed as ref to the markers to keep
  // tracks of markers currently on the map
  const setMarkerRef = useCallback((marker: Marker | null, key: string) => {
    setMarkers(markers => {
      if ((marker && markers[key]) || (!marker && !markers[key]))
        return markers;

      if (marker) {
        return { ...markers, [key]: marker };
      } else {
        const { [key]: _, ...newMarkers } = markers;

        return newMarkers;
      }
    });
  }, []);

  const handleInfoWindowClose = useCallback(() => {
    setFocusedListing(null);
  }, [setFocusedListing]);

  const handleMarkerClick = useCallback((listing: ListingListSeekers) => {
    setFocusedListing(listing.code);
    setListingOverview(listing)
  }, [setFocusedListing]);

  return (
    <>
      {data.map(item => (<React.Fragment key={item.code}>

        <ClusteredPinMap
          key={item.code}
          data={item}
          onClick={handleMarkerClick}
          setMarkerRef={setMarkerRef}
        />
        {(focusedListing == item.code) && (
          <InfoWindow
            anchor={markers[item.code]}

            onCloseClick={handleInfoWindowClose}
            headerDisabled
            style={{
              overflow: "auto !important",
              borderRadius: "24px !important"
            }}
            className="!rounded-xl"
            minWidth={240}
            maxWidth={240}
          >
            <ListingWrapper
              data={item}
              conversion={conversions}
            >
              <ListingImage forceLazyloading containerClassName="!rounded-b-none" extraHeaderAction={<>
                <Button variant={"secondary"} className="bg-white rounded-full !h-6 !w-6 hover:bg-white/80" size={"icon"} onClick={() => setFocusedListing(null)}>
                  <X className="!w-3 !h-3" />
                </Button>
              </>} />
              <div className="space-y-2 px-2 pb-2">
                <ListingTitle className="leading-6" />
                <ListingSellingPoint />
                <ListingPrice />
              </div>
            </ListingWrapper>
          </InfoWindow>
        )}
      </React.Fragment>
      ))}

    </>
  );
};