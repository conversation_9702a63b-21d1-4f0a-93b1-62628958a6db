import { FormField } from '@/components/ui/form'
import { FieldValues } from 'react-hook-form'
import { Input } from '../ui/input'
import { BaseInputForm } from '@/types/base'
import BaseInputLayout from './base-input'
import { Textarea } from '../ui/textarea'
import { ComponentProps } from 'react'

interface TextAreaInputProps<T extends FieldValues> extends BaseInputForm<T> {
  label: string,
  placeholder: string
  description?: string,
  inputProps?: ComponentProps<"textarea">
}
export default function TextAreaInput<T extends FieldValues>({ form, label, name, placeholder, description, inputProps }: TextAreaInputProps<T>) {
  return <FormField
    control={form.control}
    name={name}
    render={({ field }) => (
      <BaseInputLayout label={label} description={description}>
        <Textarea
          placeholder={placeholder}
          className="resize-none"
          {...field}
          {...inputProps}
          rows={10}

        />
      </BaseInputLayout>
    )}
  />
}