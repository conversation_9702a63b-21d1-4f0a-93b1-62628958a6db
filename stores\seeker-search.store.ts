import { toggleStringArrrayItem } from "@/lib/utils"
import moment from "moment"
import { create } from "zustand"
import { createJSONStorage, persist } from "zustand/middleware"


interface Search {
  query: string,
  propertyType: string[],
}
interface SearchHistory extends Search{
  validUntil: string 
}
interface SeekerSearch {
  query:string, // mostly for location (ex: Badung--bali)
  setQuery:(value:string) =>void,
  propertyType: string[], //type of property
  setPropertyType: (value:string) => void,
  setPropertyTypeFromArray: (value:string[]) => void,
  searchHistory: SearchHistory[],
  setSearchHistory:(data:Search) => void
  activeSearch: Search,
  setActiveSearch: (data:Search) => void,
  isOpen?:boolean,
  setIsOpen:(val:boolean) => void,
  locationInputFocused?:boolean,
  setLocationInputFocused:(val:boolean) => void
  categoryInputFocused?:boolean,
  setCategoryInputFocused:(val:boolean) => void,
  clearSearch:() => void,
  clearCategory: () => void

}

const MAXIMUM_SEARCH_HISTORY = 5

export const useSeekerSearchStore = create<SeekerSearch>()(
  persist(
    set => ({
      activeSearch: {propertyType: [],query: ""}, // search that currently used by user
      propertyType: [], // for storing temp search for property type 
      query: "", // for storing temp query for property type
      searchHistory: [], // for past search done by user
      isOpen: true,
      locationInputFocused:false,
      categoryInputFocused:false,
      setActiveSearch: (activeSearch) => set(({activeSearch})),
      setPropertyType: (data) => set(state => ({propertyType: toggleStringArrrayItem(state.propertyType,data)})),
      setQuery: (query) => set(({query})),
      setSearchHistory: (data) => set(state => {
        const search:SearchHistory = {...data,validUntil: moment().add(7,"days").format("DD-MMM-YYYY")}
        
        // check if search already on searchHistory don't add it again
        const isSame = state.searchHistory.findIndex(item => {
          const sameQuery = item.query == search.query
          return sameQuery 
        }) 
        if(isSame >= 0) return state 

        const searchHistory = [...state.searchHistory,search]
        // check of if search history is not full (total not exceeding MAXIMUM_SEARCH_HISTORY) 
        if(state.searchHistory.length  < MAXIMUM_SEARCH_HISTORY){
          state.searchHistory = searchHistory
          return state
        }
        // pop last array before add new search if the search history is full
        const history = searchHistory.slice(1,4)
        state.searchHistory = [...history, search]
        return state
      }),
      setIsOpen: (isOpen) => set(({isOpen})),
      setCategoryInputFocused: (val:boolean) => set(({categoryInputFocused:val})),
      setLocationInputFocused: (val:boolean) => set(({locationInputFocused:val})),
      clearSearch: () => set(({query: "",propertyType: []})),
      setPropertyTypeFromArray: (val:string[]) => set(({propertyType: val})),
      clearCategory: () => set(({propertyType: []})),
    }),{
      name: "seeker-search",
      storage: createJSONStorage(() => localStorage),
      onRehydrateStorage(state) {
          if(!state) return
          const filteredHistorySearch = state.searchHistory.filter(entry => {
            const entryDate = moment(entry.validUntil)
            return moment().isSameOrBefore(entryDate)
          })
          state.searchHistory = filteredHistorySearch
      },
    }
  )
)