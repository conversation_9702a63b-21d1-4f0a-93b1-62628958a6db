import { Staff } from "@/core/domain/staff/staff";
import { StaffDto } from "./dto";

export function transfromStaff(dto:StaffDto[]):Staff[]{
  const data:Staff[] = dto.map(item => ({
    createdAt: item.created_at,
    email: item.email,
    fullname: item.fullname,
    id: item.id,
    phoneCode: item.phone_code,
    phoneNumber: item.phone_number,
    role: {
      code: item.role.code,
      name: item.role.name
    }
  }))
  return data
}

export function transformStaffDetail(dto:StaffDto):Staff{
  return {
    createdAt: dto.created_at,
    email: dto.email,
    fullname: dto.fullname,
    id: dto.id,
    phoneCode: dto.phone_code,
    phoneNumber: dto.phone_number,
    role: {
      code: dto.role.code,
      name: dto.role.name
    }
  }
}