import { create } from "zustand"

interface MutationStore {
  isMutating: boolean,
  setMutating: (value:boolean) => void
  mutationResult?: "SUCCESS"| "ERROR",
  setMutationResult: (value: "SUCCESS" | "ERROR" | undefined) => void
  mutationId?:string,
  setMutationId: (value:string) => void
}

export const useMutationStore = create<MutationStore>(set => ({
  isMutating: false,
  setMutating: (value) => set({isMutating: value}),
  setMutationResult: (value) => set({mutationResult: value}),
  mutationId: undefined,
  setMutationId: (value) => set({mutationId: value}),
  mutationResult: undefined
}))