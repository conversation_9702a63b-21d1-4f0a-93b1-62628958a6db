"use client"
import { AdvancedMarker, AdvancedMarkerAnchorPoint, Map } from "@vis.gl/react-google-maps"
import { Circle } from "@/components/google-maps/circle"
import ListingCategoryIcon from "../../../s/listing-category-icon"
import { useTranslations } from "next-intl"
import { useState } from "react"
import { useUserStore } from "@/stores/user.store"
import { packages } from "@/core/domain/subscription/subscription"
import dynamic from 'next/dynamic';


const SubscribeMapBanner = dynamic(() => import("@/components/subscribe/subscribe-map-banner"), { ssr: false })
const GoogleMapsProvider = dynamic(() => import("@/components/providers/google-maps-provider"), { ssr: false })

export default function PropertyMap({ lat, lng, category }: { lat: number, lng: number, category: string }) {
  const t = useTranslations("seeker")
  const [showBanner, setShowBanner] = useState(false)
  const [zoomLevel, setZoomLevel] = useState(12)
  const { seekers } = useUserStore()
  return <>
    <GoogleMapsProvider>
      <h2 className="text-2xl font-bold">{t('misc.mapLocation')}</h2>
      <div className="w-full h-full min-h-[400px] overflow-hidden rounded-2xl relative">
        {showBanner &&
          <>
            <SubscribeMapBanner className="top-4 text-center" />
          </>
        }

        <Map
          reuseMaps
          mapId={process.env.NEXT_PUBLIC_GOOGLE_MAPS_ID}
          style={{ width: "100%", height: "100%" }}
          className="!h-[400px]"
          mapTypeControl={false}
          fullscreenControl={false}
          defaultZoom={12}
          defaultCenter={{ lat, lng }}
          center={{ lat, lng }}
          maxZoom={seekers.accounts.zoomFeature.max}
          minZoom={12}
          disableDefaultUI
          onZoomChanged={e => {
            if (e.detail.zoom >= seekers.accounts.zoomFeature.max
              && zoomLevel !== e.detail.zoom
              && seekers.accounts.membership == packages.free
            ) {
              setShowBanner(true)

            } else {
              setShowBanner(false)
            }
            setZoomLevel(e.map.getZoom()!)


          }}

        >{
            seekers.accounts.membership == packages.free &&
            <Circle
              center={{ lat, lng }}
              radius={2000}
              strokeColor={'#B48B55'}
              strokeOpacity={1}
              strokeWeight={3}
              fillColor={'#B48B55'}
              fillOpacity={0.2}
            />
          }
          <AdvancedMarker
            position={{ lat, lng }}
            anchorPoint={AdvancedMarkerAnchorPoint.CENTER}
          >
            <div className={"w-12 h-12 bg-white text-white flex items-center justify-center py-3 rounded-full text-sm font-medium shadow-md border"}>

              <ListingCategoryIcon category={category || ""} className={"!w-4 !h-4 text-seekers-primary"} />
            </div>

          </AdvancedMarker>
        </Map>
      </div>
    </GoogleMapsProvider>
  </>
}