import { postTotpVerificationCode } from "@/core/infrastructures/auth";
import { TotpVerificationCode } from "@/core/infrastructures/auth/dto";
import { useMutation } from "@tanstack/react-query";

// TOPT = Two Factor Authtentication
export function useVerifyTotp() {
  const mutation = useMutation({
    mutationFn: (data: TotpVerificationCode) => postTotpVerificationCode(data),
  });
  return mutation;
}
