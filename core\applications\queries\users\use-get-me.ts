import { getMeService } from "@/core/infrastructures/user/services";
import { ACCESS_TOKEN } from "@/lib/constanta/constant";
import { baseUser, useUserStore } from "@/stores/user.store";
import { useQuery } from "@tanstack/react-query";
import Cookies from "js-cookie";
export const MY_DETAIL_QUERY_KEY = "my-detail";
export function useGetMyDetail(enable = true) {
  const { setSeekers, clearUser, setRole } = useUserStore((state) => state);
  const accessToken = Cookies.get(ACCESS_TOKEN);

  const query = useQuery({
    queryKey: [MY_DETAIL_QUERY_KEY, accessToken || "0"],
    queryFn: async () => {
      if (!accessToken) {
        return baseUser;
      }
      try {
        const data = await getMeService();
        setSeekers(data);
        setRole("SEEKER");
        return data;
      } catch (err: unknown) {
        clearUser();
        return baseUser;
      }
    },
    refetchOnWindowFocus: false,

    retry: false,
    enabled: enable,
  });
  return query;
}
