import { getAllUsersService } from "@/core/infrastructures/user/services";
import { BasePaginationRequest } from "@/types/base";
import { useQuery } from "@tanstack/react-query";

export const ALL_USER_QUERY_KEY = "all-user"
export function useGetAllUser(data:BasePaginationRequest){
  const {
    page,
    per_page,
    search
  } = data
  const query = useQuery({
    queryKey: [ALL_USER_QUERY_KEY,page,per_page,search],
    queryFn: async () => {
      const data:BasePaginationRequest = {
        page,per_page,search
      } 
      return await getAllUsersService(data)
    },
    retry: 0
  },
)
  return query
}