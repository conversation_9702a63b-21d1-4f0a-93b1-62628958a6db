import { MessageItem } from "@/core/domain/messages/messages";
import { useMessagingStore } from "@/stores/messaging.store";
import { useUserStore } from "@/stores/user.store";
import { ReceiverName } from "./receiver-name";
import { cn, timeFormatter } from "@/lib/utils";
import { useChat } from "@/hooks/use-chat";
import Image from "next/image";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { socket } from "@/core/utils/socket";

export default function ChatItemPreview({ participant, lastMessages, code, category }: MessageItem) {
  const { setRoomId, setlayout, roomId, setParticipant } = useMessagingStore(state => state)
  const { leaveRoom } = useChat()
  const { code: userCode } = useUserStore(state => state.seekers)
  const isSender = lastMessages.displayAs !== userCode
  const name = (participant?.fullName || "").trim().split(" ")
  const nameLength = name.length
  const handleOpenDetail = () => {
    if (code == roomId) return

    socket.emit("joinRoomChat", { code: roomId }, () => { });
    leaveRoom()
    setRoomId(code)
    setlayout("detail-chat")
    setParticipant({
      category: participant?.category || "",
      email: participant?.email || "",
      fullName: participant?.fullName || "",
      id: participant?.id || "",
      phoneNumber: participant?.phoneNumber || "",
      status: participant?.status || "",
      image: participant?.image || "",
      property: {
        image: participant?.property?.image || "",
        title: participant?.property?.title || ""
      }

    })
  }
  if (!lastMessages) {
    return <></>
  }
  return <div
    className={`rounded-lg w-full overscroll-none ${roomId == code ? "bg-seekers-primary-lighter/30" : ""} hover:bg-seekers-primary-lighter/60 py-4 px-2`}
    onClick={handleOpenDetail}>
    <div className="flex gap-2 ">
      <div className="min-w-9 !w-9 !h-9 aspect-square rounded-full bg-neutral-200 relative overflow-hidden">
        <Avatar className={cn("w-full rounded-full bg-seekers-text-lighter")}>
          <AvatarImage src={participant?.property?.image || participant?.image || ""} className="border" />
          <AvatarFallback className="bg-transparent text-white">
            <span>{name[0][0]}{name[nameLength / 2]?.[0] || ""}</span>
          </AvatarFallback>
        </Avatar>
      </div>
      <div className="flex-grow">
        <div className="flex items-center">
          <ReceiverName name={participant?.property?.title || participant?.fullName} category={""} />
          <p className="text-[10px] text-seekers-text-light">{timeFormatter(lastMessages?.createdAt)}</p>
        </div>
        <div className="flex justify-between items-center">
          <p className="text-xs text-seekers-text-light line-clamp-1">{lastMessages?.text}</p>
          {!lastMessages.isRead && isSender
            ?
            <div className="w-2 aspect-square rounded-full bg-seekers-primary"></div>
            : <></>
          }
        </div>
      </div>
    </div>
  </div>
}

