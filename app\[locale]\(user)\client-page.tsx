"use client"
import dynamic from 'next/dynamic';

const ClearSearchHelper = dynamic(() => import("./clear-search-helper"), { ssr: false })
const SocialAuthFormatter = dynamic(() => import('../(auth)/social-auth'), { ssr: false })
const EmailDiscountPopup = dynamic(() => import("@/components/pop-up/email-discount-pop-up"))

export default function ClientPage({ statusCode, accessToken, expiredAt }: { statusCode?: string, accessToken?: string, expiredAt?: string }) {
  return <>
    <SocialAuthFormatter accessToken={accessToken} status={statusCode} expired={expiredAt || "28800"} />
    <ClearSearchHelper />
    <EmailDiscountPopup />

  </>

}