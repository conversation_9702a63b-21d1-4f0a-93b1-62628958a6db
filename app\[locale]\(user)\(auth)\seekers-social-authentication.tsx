"use client"
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useFacebookAuth } from "@/core/applications/mutations/auth/use-facebook-auth";
import { useGoogleAuth } from "@/core/applications/mutations/auth/use-google-auth";
import { useTranslations } from "next-intl";
import { FaFacebook } from "react-icons/fa";
import { FcGoogle } from "react-icons/fc";

export default function SeekersSocialAuthentication() {
  const googleAuthMutation = useGoogleAuth()
  const facebookAuthMutation = useFacebookAuth()
  const handleGoogleAuth = async () => {
    await googleAuthMutation.mutateAsync()
  }
  const handleFacebookAuth = async () => {
    await facebookAuthMutation.mutateAsync()
  }
  return <div className="grid md:grid-cols-2 gap-4">
    <Button
      className="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background shadow-sm hover:text-accent-foreground h-9 px-4 py-2 w-full rounded-xl border-gray-200 hover:bg-gray-50"
      variant={"outline"}
      onClick={handleGoogleAuth}
      type="button"
    >
      <FcGoogle className="mr-2 h-4 w-4" />
      Google
    </Button>
    <Button
      className="inline-flex w-full items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background shadow-sm hover:text-accent-foreground h-9 px-4 py-2 rounded-xl border-gray-200 hover:bg-gray-50"
      variant={"outline"}
      onClick={handleFacebookAuth}
      type="button"
    >
      <FaFacebook className="mr-2 h-4 w-4" />
      Facebook
    </Button>
  </div>
}