export interface ChatParticipant {
  id: string;
  email: string;
  phone_code: string;
  phone_number: string;
  type: string;
  image: string;
  display_name: string;
}
type PropertyImage = {
  is_highlight: boolean;
  image: string;
};

export interface RefData {
  id: string;
  code: string;
  title: string;
  excerpt: string;
  description: string;
  images: PropertyImage[];
  status: string;
  expiry_date: string;
  adjusted_expiry_date: string | null;
  updated_at: string;
  extended_list?: {
    id: string;
    images: PropertyImage[];
    title: string;
    code: string;
  }[];
}
export interface MessageDto {
  code: string;
  id: string;
  category: string;
  created_at: string;
  messages: MessageTextDto[];
  participants: {
    room_id: string;
    info: ChatParticipant;
  };
  status: string;
  updated_at: string;
  ref_data: RefData;
}

export interface MessageTextDto {
  id: string;
  created_at: string;
  display_as: string;
  display_name: string;
  is_read: boolean;
  is_send: boolean;
  room_id: string;
  text: string;
  code: string;
  status?: string;
}

export interface NewChatDto {
  requested_by: "CLIENT";
  category: "LISTING" | "CUSTOMER_SUPPORT" | "SEEKER_OWNER" | "CHATTING";
  message: string;
  ref_id?: string;
  receiver?: string;
}

export interface GetChatsDto {
  search?: string;
  status?: string;
}
