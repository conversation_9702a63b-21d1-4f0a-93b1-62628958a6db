import { useEffect, useState } from "react";

export function useWebsocket(url: string) {
  const [messages, setMessages] = useState<string[]>([]);
  const [webSocket, setWebsocket] = useState<WebSocket | null>(null);
  useEffect(() => {
    const socket = new WebSocket(url);
    setWebsocket(socket);
    socket.onmessage = (event) => {
      setMessages((prev) => [...prev, event.data]);
    };
    return () => {
      socket.close();
    };
  }, [url]);

  const sendMessage = (message: string) => {
    if (!webSocket) return;
    webSocket.send(message);
  };
  return { messages, sendMessage };
}
