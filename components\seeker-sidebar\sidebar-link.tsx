import { cn } from "@/lib/utils"
import Link from "next/link"
import React from "react"

interface SidebarLinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  active?: boolean
  href: string
}


export const SidebarLink = React.forwardRef<HTMLAnchorElement, SidebarLinkProps>(
  ({ className, active, href, ...props }, ref) => {
    return (
      <Link
        href={href}
        ref={ref}
        className={cn(
          "flex items-center py-2 pl-6 hover:bg-gray-100 rounded-md transition-colors",
          active && "bg-[#FAF6F0] text-[#C19B67]",
          className,
        )}
        {...props}
      />
    )
  },
)
SidebarLink.displayName = "SidebarLink"