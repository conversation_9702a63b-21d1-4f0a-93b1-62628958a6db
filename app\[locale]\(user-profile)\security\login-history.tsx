import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { CheckCircle2, History } from "lucide-react";
import { useTranslations } from "next-intl";

export default function LoginHistory() {
  const t = useTranslations("seeker")

  const recentLogins = [
    {
      date: new Date("2025-02-02 14:30").toLocaleDateString(),
      device: "MacBook Pro",
      location: "Amsterdam, Netherlands",
      status: "Success",
    },
    {
      date: new Date("2025-02-01 09:15").toLocaleDateString(),
      device: "iPhone 15",
      location: "Utrecht, Netherlands",
      status: "Success",
    },
    {
      date: new Date("2025-01-31 18:45").toLocaleDateString(),
      device: "Chrome on Windows",
      location: "Den Haag, Netherlands",
      status: "Success",
    },
  ]

  return <Card>
    <CardHeader className="flex max-sm:flex-col max-sm:items-start max-sm:gap-4 max-sm:pb-4 flex-row items-center justify-between space-y-0 pb-2">
      <div className="space-y-1">
        <CardTitle className="text-seekers-primary flex items-center">
          <History className="mr-2 h-4 w-4" />
          {t("setting.profile.security.loginHistory.title")}
        </CardTitle>
        <CardDescription>{t("setting.profile.security.loginHistory.description")}</CardDescription>
      </div>
      <Button
        size={"sm"}
        variant="outline"
        className="border-seekers-primary text-seekers-primary hover:text-seekers-primary hover:bg-seekers-primary/10">
        {t("cta.viewAll")}
      </Button>
    </CardHeader>
    <CardContent>
      <Table>
        <TableHeader>
          <TableRow className="hover:bg-transparent">
            <TableHead>{t("setting.profile.security.loginHistory.table.date")}</TableHead>
            <TableHead>{t("setting.profile.security.loginHistory.table.device")}</TableHead>
            <TableHead>{t("setting.profile.security.loginHistory.table.location")}</TableHead>
            <TableHead>{t("setting.profile.security.loginHistory.table.status")}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {recentLogins.map((login, index) => (
            <TableRow key={index} className="hover:bg-transparent">
              <TableCell>{login.date}</TableCell>
              <TableCell>{login.device}</TableCell>
              <TableCell>{login.location}</TableCell>
              <TableCell>
                <Badge variant={"outline"} className="bg-green-500/10 text-green-500">
                  {login.status}
                </Badge>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </CardContent>
  </Card>
}