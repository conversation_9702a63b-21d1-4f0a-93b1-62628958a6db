import ImageUrlBuilder from "@sanity/image-url";
import { createClient, type QueryParams } from "next-sanity";
import clientConfig from "./client-config";
import {
  homepageSeoContent,
  morePostsBasedOnCategoryQuery,
  postQuery,
  postQueryBySlug,
  postQueryHomepage,
  privacyPolicyContent,
  TermsOfUseContent,
  userDataDeletionContent,
} from "./query";
import { Blog, SeoContent, DefaultContent } from "./types";

export const client = createClient(clientConfig);
export function imageBuilder(source: string) {
  return ImageUrlBuilder(clientConfig).image(source);
}

export async function sanityFetch<QueryResponse>({
  query,
  qParams,
  tags,
}: {
  query: string;
  qParams: QueryParams;
  tags: string[];
}): Promise<QueryResponse> {
  return client.fetch<QueryResponse>(query, qParams, {
    next: { tags, revalidate: 3600 },
  });
}

export const getHomepagePosts = async () => {
  const data: Blog[] = await sanityFetch({
    query: postQueryHomepage,
    qParams: {},
    tags: ["post", "author", "category"],
  });
  return data;
};
export const getPosts = async () => {
  const data: Blog[] = await sanityFetch({
    query: postQuery,
    qParams: {},
    tags: ["post", "author", "category"],
  });
  return data;
};

export const getPostBySlug = async (slug: string) => {
  const data: Blog = await sanityFetch({
    query: postQueryBySlug,
    qParams: { slug },
    tags: ["post", "author", "category"],
  });

  return data;
};

export const getPostByCategory = async (slug: string, id: string) => {
  const data: Blog[] = await sanityFetch({
    query: morePostsBasedOnCategoryQuery,
    qParams: { slug, id },
    tags: [],
  });
  return data;
};

export const getHomepageSeoContent = async (language: string) => {
  const data: SeoContent[] = await sanityFetch({
    query: homepageSeoContent,
    qParams: { language },
    tags: [],
  });

  return data;
};

export const getTermsOfUseContent = async (language: string) => {
  const data: DefaultContent[] = await sanityFetch({
    query: TermsOfUseContent,
    qParams: { language },
    tags: [],
  });
  return data;
};

export const getPrivacyPolictContent = async (language: string) => {
  const data: DefaultContent[] = await sanityFetch({
    query: privacyPolicyContent,
    qParams: { language },
    tags: [],
  });
  return data;
};

export const getUserDeletionContent = async (language: string) => {
  const data: DefaultContent[] = await sanityFetch({
    query: userDataDeletionContent,
    qParams: { language },
    tags: [],
  });
  return data;
};
