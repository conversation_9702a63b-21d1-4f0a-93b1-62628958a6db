import useSearchParamWrapper from "./use-search-param-wrapper"

export default function useFilterItem(filterKey:string){
  const { createQueryString, searchParams, removeQueryParam } = useSearchParamWrapper() 
  
  const checkFilterIsActive = (filter:string) => !searchParams.get(filterKey)?.includes(filter)  
  const handleAddFilter = (value: string) => {
    let filter = searchParams.get(filterKey) || ""
    if (!filter) {
      createQueryString(filterKey, value)
      return
    }
    const isContain = filter?.includes(value)
    if (isContain) {
      const newFilter = filter.replaceAll(",", " ").replace(value, "").trim()
      const finalFilter = newFilter.replace(/\s+/g, ' ')
      if (newFilter.length < 1) {
        removeQueryParam([filterKey])
      } else {
        const filterArray = finalFilter.split(" ")
        filterArray.filter((value:string) => value !== "")
        createQueryString(filterKey, filterArray.toString())
      }
    } else {
      const filterArray = filter.split(",")
      filterArray.push(value)
      createQueryString(filterKey, filterArray.toString())
    }
  }
  return {handleAddFilter, checkFilterIsActive}
}