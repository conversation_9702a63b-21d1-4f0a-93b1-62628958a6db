import { BasePaginationRequest } from "@/types/base";
import { getDetailStaff, getStaffs } from "./api";
import { transformMeta } from "../utils/transform";
import { transformStaffDetail, transfromStaff } from "./transform";

export async function getAllStaffService(searchParam:BasePaginationRequest){
  try{
    const request = await getStaffs(searchParam)
    return {
      data: transfromStaff(request.data.data.items), 
      meta:transformMeta(request.data.data.meta)
    }
  }catch(e){
    throw new Error(e as any)
  }
}

export async function getStaffDetailService(id:string){
  try{
    const request = await getDetailStaff(id)
    return transformStaffDetail(request.data.data)
  }catch(e){
    throw new Error(e as any)
  }
}
