import { postSubscriptionPackage } from "@/core/infrastructures/subscription/api";
import {
  PostSubscriptionDto,
  PostSubscriptionResponse,
} from "@/core/infrastructures/subscription/dto";
import { useMutation } from "@tanstack/react-query";

export function useSubscribePlan() {
  const mutation = useMutation({
    mutationFn: async (data: PostSubscriptionDto) =>
      await postSubscriptionPackage(data),
  });
  return mutation;
}
