import { BaseInputForm } from "@/types/base";
import { FieldValues } from "react-hook-form";
import { FormControl, FormDescription, FormField, FormLabel } from "../ui/form";
import BaseInputLayout from "./base-input";
import { Switch } from "../ui/switch";
interface ToggleInputProps<T extends FieldValues> extends BaseInputForm<T> {
  label: string,
  description?: string,
  children?: React.ReactNode,
  isEditable?: boolean,
}

export default function ToggleInput<T extends FieldValues>({ form, label, name, isEditable, description }: ToggleInputProps<T>) {
  return <FormField
    control={form.control}
    name={name}
    render={({ field }) => (
      <BaseInputLayout>
        <div className="flex gap-1 justify-between items-center">
          <div className="space-y-1 leading-none">
            <FormLabel>
              {label}
            </FormLabel>
            {
              description &&
              <FormDescription>
                {description}
              </FormDescription>
            }
          </div>
          <FormControl>
            <Switch
              checked={field.value}
              onCheckedChange={field.onChange}
              disabled={field.disabled}
            />
          </FormControl>

        </div>
      </BaseInputLayout>

    )}
  />
}