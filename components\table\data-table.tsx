"use client"

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  SortingState,
  VisibilityState,
  useReactTable,
  Row,
} from "@tanstack/react-table"

import { Input } from "@/components/ui/input"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { DataTablePagination } from "./pagination"
import React, { useEffect, useState } from "react"
import { DataTableViewOptions } from "./toggle-column"
import { useTranslations } from "next-intl"
import { Skeleton } from "../ui/skeleton"
import { BasePaginationRequest } from "@/types/base"
import { BaseMeta } from "@/core/domain/utils/utils"

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[],
  hasFilter?: boolean,
  additionalFilter?: React.ReactNode
  customNoResult?: React.ReactNode
  customSearchFilter?: React.ReactNode,
  isLoading: boolean,
  searchValue: string,
  onSearch: (value: React.ChangeEvent<HTMLInputElement>) => void,
  onRowClick?: (row: Row<TData>) => void
  serverPagination?: BasePaginationRequest,
  isClientPagination?: boolean,
  meta?: BaseMeta,
  helperColumnFilter?: (val: string) => string,
  inititalColumnVisibility?: VisibilityState,
  showTableView?: boolean,
  disableRowPerPage?: boolean
}

export function DataTable<TData, TValue>({
  columns,
  data,
  hasFilter = true,
  additionalFilter,
  customNoResult = "No results.",
  customSearchFilter,
  isLoading = false,
  onRowClick,
  searchValue,
  onSearch,
  meta,
  isClientPagination,
  inititalColumnVisibility,
  helperColumnFilter,
  showTableView = true,
  disableRowPerPage
}: DataTableProps<TData, TValue>) {
  const t = useTranslations()
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(inititalColumnVisibility || {})
  const [rowSelection, setRowSelection] = useState({})
  const [hasData, setHasData] = useState(false)

  useEffect(() => {
    if (!inititalColumnVisibility) return
    setColumnVisibility(inititalColumnVisibility)
  }, [inititalColumnVisibility])

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    }
  })
  useEffect(() => {
    const hasData = table.getRowModel().rows.length > 0
    setHasData(hasData)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [table.getRowModel().rows.length])

  return (
    <>
      <div className="flex flex-wrap items-center justify-end gap-2">
        {
          hasFilter ?
            <>
              {customSearchFilter ||
                <Input
                  placeholder={t('component.dataTable.filterData')}
                  value={searchValue ?? ""}
                  onChange={onSearch}
                  className=""
                />
              }
            </>
            :
            <>
            </>

        }
        {showTableView &&
          <DataTableViewOptions table={table} helperColumnFilter={helperColumnFilter} />
        }
        {
          additionalFilter
        }
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {
                        header.isPlaceholder
                          ? null
                          : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )
                      }
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {
              isLoading ?
                [1, 2, 3].map(item => {
                  return <TableRow key={item}>
                    {table.getVisibleFlatColumns().map((item, idx) => (
                      <TableCell key={idx}>
                        <Skeleton className="w-full h-4" />
                      </TableCell>
                    ))}
                  </TableRow>
                }) :
                table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      onClick={() => onRowClick?.(row)}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      {customNoResult}
                    </TableCell>
                  </TableRow>
                )}
          </TableBody>
        </Table>
      </div>
      {
        hasData &&
        <DataTablePagination disableRowPerPage={disableRowPerPage} table={table} meta={meta} isClientPagination={isClientPagination} />
      }

    </>
  )
}
