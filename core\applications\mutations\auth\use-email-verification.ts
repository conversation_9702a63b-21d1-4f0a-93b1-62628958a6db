import { sendEmailverification } from "@/core/infrastructures/auth";
import { SendEmailVerificationDto } from "@/core/infrastructures/auth/dto";
import { useToast } from "@/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

export function useEmailVerification(onSuccess:(response?:unknown) => void){
  const {toast} = useToast()
  const t =useTranslations("universal")
  const mutation = useMutation({
    mutationFn: (data:SendEmailVerificationDto) => sendEmailverification(data),
    onSuccess: (response) => {
      onSuccess(response)
    },
    onError: (error) => {
      const data: any = (error as any).response.data
      if(data.message.includes("is already sent")){
        onSuccess(error)
        return
      }
      toast({
        title: t('misc.foundError'),
        description: data.message,
        variant: "destructive"
      })
    },
  })
  return mutation
}