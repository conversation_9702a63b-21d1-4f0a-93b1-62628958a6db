"use client"
import { useTranslations } from "next-intl"
import { FieldValues, useForm } from "react-hook-form"
import { notificationFormSchema } from "./notification-form.schema"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { useEffect, useState } from "react"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { Switch } from "@/components/ui/switch"
import { BaseInputForm } from "@/types/base"
import { useUpadeUserDetail } from "@/core/applications/mutations/user/use-update-user-detail"
import { SettingsUser, UpdateUserDto } from "@/core/infrastructures/user/dto"
import { useToast } from "@/hooks/use-toast"
import { useUserStore } from "@/stores/user.store"

type FormListType<T extends FieldValues> = BaseInputForm<T> & {
  label: string,
  description: string,
  key: keyof SettingsUser
}
export default function NotificationForm() {
  const t = useTranslations("seeker")
  type notificationFormType = z.infer<typeof notificationFormSchema>
  const [isSaving, setIsSaving] = useState(false)
  const user = useUserStore(state => state.seekers)
  const updateSettingMutation = useUpadeUserDetail()
  const form = useForm<notificationFormType>({
    resolver: zodResolver(notificationFormSchema),
    defaultValues: {
      soundNotifications: user.setting.soundNotif,
      emailNewMessages: user.setting.messageNotif,
      emailNewProperties: user.setting.priceAlertNotif,
      priceChangeAlerts: user.setting.priceAlertNotif,
      newsletters: user.setting.newsletterNotif,
      specialOffers: user.setting.specialOfferNotif,
      surveys: user.setting.surveyNotif,
    },
  })

  useEffect(() => {
    if (user.setting) {
      form.setValue("emailNewMessages", user.setting.messageNotif || false)
      form.setValue("emailNewProperties", user.setting.propertyNotif || false)
      form.setValue("newsletters", user.setting.newsletterNotif || false)
      form.setValue("priceChangeAlerts", user.setting.priceAlertNotif || false)
      form.setValue("soundNotifications", user.setting.soundNotif || false)
      form.setValue("specialOffers", user.setting.specialOfferNotif || false)
      form.setValue("surveys", user.setting.surveyNotif || false)
    }
  }, [user.setting, form])
  const { toast } = useToast()
  const formList: FormListType<notificationFormType>[] = [
    {
      name: "soundNotifications",
      label: t('setting.notification.soundNotification.title'),
      description: t('setting.notification.soundNotification.description'),
      form: form,
      key: "sound_notif"
    },
    {
      name: "emailNewMessages",
      label: t('setting.notification.emailForNewMessages.title'),
      description: t('setting.notification.emailForNewMessages.description'),
      form: form,
      key: "message_notif"

    },
    {
      name: "emailNewProperties",
      label: t('setting.notification.emailForNewProperties.title'),
      description: t('setting.notification.emailForNewProperties.description'),
      form: form,
      key: "property_notif"
    },
    {
      name: "priceChangeAlerts",
      label: t('setting.notification.priceChangeAlert.title'),
      description: t('setting.notification.priceChangeAlert.description'),
      form: form,
      key: "price_alert_notif"
    },
    {
      name: "newsletters",
      label: t('setting.notification.newsletter.title'),
      description: t('setting.notification.newsletter.description'),
      form: form,
      key: "newsletter_notif"
    },
    {
      name: "specialOffers",
      label: t('setting.notification.specialOffer.title'),
      description: t('setting.notification.specialOffer.description'),
      form: form,
      key: "special_offer_notif"
    },
    {
      name: "surveys",
      label: t('setting.notification.surveys.title'),
      description: t('setting.notification.surveys.description'),
      form: form,
      key: "survey_notif"
    }
  ]
  const onSubmit = (data: notificationFormType) => {
    setIsSaving(true)
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false)
    }, 1000)
  }

  const onChange = async (label: string, formItem: BaseInputForm<notificationFormType>, key: keyof SettingsUser, value: boolean) => {
    const data: UpdateUserDto = {
      settings: {
        [key]: value
      }
    }
    try {
      await updateSettingMutation.mutateAsync(data)
      toast({
        title: t('success.updateNotification.title', { field: label })
      })
      form.setValue(formItem.name, value)
    } catch (e) {
      toast({
        title: t('error.updateNotification.title', { field: label })
      })
    }

  }

  return <>
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8 pb-8">
        <div className="space-y-4">
          {
            formList.map((item, idx) => <FormField
              key={idx}
              control={form.control}
              name={item.name}
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">{item.label}</FormLabel>
                    <FormDescription>{item.description}</FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      className="data-[state=checked]:bg-seekers-primary"
                      onCheckedChange={val => onChange(
                        item.label,
                        {
                          form: form,
                          name: item.name
                        },
                        item.key,
                        val
                      )} />
                  </FormControl>
                </FormItem>
              )}
            />
            )
          }
        </div>
      </form>
    </Form>
  </>
}