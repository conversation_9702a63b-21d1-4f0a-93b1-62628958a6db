import {
  packages,
  Subscription,
} from "@/core/domain/subscription/subscription";
import { SubscriptionPackagesDto } from "./dto";

export function transfromSubscriptionPackages(
  dto: SubscriptionPackagesDto[]
): Subscription[] {
  const finderData = dto.filter((item) => item.name == packages.finder);
  const finderPriceData = finderData[0].price_option.filter(
    (item) => item.price_unit == "eur"
  );
  const achieverData = dto.filter((item) => item.name == packages.archiver);
  const achieverPriceData = achieverData[0].price_option.filter(
    (item) => item.price_unit == "eur"
  );
  const cleanFinderData: Subscription = {
    name: finderData[0].name,
    currency: "eur",
    priceVariant: finderPriceData.map((item) => ({
      price: item.price / 100,
      currency: item.price_unit,
      cycleCount: item.cycle_count,
      cycleUnit: item.cycle_unit,
      priceId: item.ref_price_id,
    })),
    productId: finderData[0].ref_product_id,
  };
  const cleanAchieverData: Subscription = {
    name: achieverData[0].name,
    currency: "eur",
    priceVariant: achieverPriceData.map((item) => ({
      price: item.price / 100,
      currency: item.price_unit,
      cycleCount: item.cycle_count,
      cycleUnit: item.cycle_unit,
      priceId: item.ref_price_id,
    })),
    productId: achieverData[0].ref_product_id,
  };
  return [cleanFinderData, cleanAchieverData];
}
