import { BasePaginationRequest } from "@/types/base";

type ListingTypeDto =
  | "draft"
  | "rejected"
  | "all"
  | "active"
  | "need-credit"
  | "need-review";
export interface GetListingParam extends BasePaginationRequest {
  type?: ListingTypeDto;
  extraParam?: string;
}

export interface ItemWithSuffixDto {
  value: string;
  suffix: string | null;
}
export interface ItemWithDetailDto {
  id: string;
  title: string;
  value: string;
  suffix: string | null;
}
export interface ItemWithNameDto {
  name: string;
  type: string;
}

export interface ListingImageDto {
  id: string;
  property_id: string;
  image: string;
  order: number;
  is_highlight: boolean;
  created_at: string;
  updated_at: string;
  deleted_at: null;
}
export interface ListingListDto {
  account_id: string;
  adjusted_expiry_date?: string;
  expiry_date?: string;
  code: string;
  title: string;
  owner: string;
  location: string;
  geolocation: string;
  status: ListingTypeDto;
  price: number;
  thumbnail: ListingImageDto[];
}

export interface BasicInformationDto {
  id: string;
  property_id: string;
  option: ItemWithNameDto;
  property_of_view: string;
  years_of_building: number;
  land_size: ItemWithSuffixDto;
  building_size: ItemWithSuffixDto;
  garden_size: ItemWithSuffixDto;
  bedroom_total: ItemWithSuffixDto;
  type_bedroom: string;
  bathroom_total: ItemWithSuffixDto;
  type_cleaning: string;
  wifi_service: ItemWithSuffixDto;
  cleaning_service: ItemWithSuffixDto;
  water_fee: boolean;
  garbage_fee: boolean;
  village_fee: boolean;
  casco_status: boolean;
  created_at: string;
  updated_at: string;
}
export interface LocationDto {
  id: string;
  property_id: string;
  province: string;
  city: string;
  district: string;
  postal_code: string;
  main_address: string;
  second_address: string | null;
  type: ItemWithDetailDto;
  road_size: ItemWithSuffixDto;
  latitude: number;
  longitude: number;
  created_at: string;
  updated_at: string;
  additional_address?: {
    id?: string;
    title?: string;
    value?: string;
    is_active?: boolean;
    suffix?: string | null;
  };
}
export interface AvailabilityDto {
  id: string;
  property_id: string;
  type: ItemWithDetailDto;
  price: number;
  is_negotiable: boolean;
  available_at: string;
  duration_min: number | null;
  duration_max: number | null;
  created_at: string;
  updated_at: string;
  duration_max_unit: ItemWithSuffixDto | null;
  duration_min_unit: ItemWithSuffixDto | null;
  duration: number;
  duration_unit: ItemWithSuffixDto;
}

export interface FeatureDto {
  id: string;
  property_id: string;
  electricity: number;
  amenities: ItemWithDetailDto[];
  selling_points: ItemWithDetailDto[];
  furnishing_option: ItemWithDetailDto | null;
  parking_option: ItemWithDetailDto | null;
  living_option: ItemWithDetailDto | null;
  pool_option: ItemWithDetailDto | null;
  created_at: string;
  updated_at: string;
}

export interface ListingDetailDto {
  id: string;
  title: string;
  excerpt: string;
  description: string;
  detail: BasicInformationDto;
  location: LocationDto;
  availability: AvailabilityDto;
  features: FeatureDto;
  images: ListingImageDto[];
  status: ListingTypeDto;
  _count?: {
    favorites?: number;
  };
}
export interface ListingSeekerDetailDto extends ListingDetailDto {
  owner: {
    full_name: string;
    image: string;
    user: {
      id: string;
      code: string;
      email: string;
      phone_code: string;
      phone_number: string;
    };
  };
  middleman: {
    full_name: string;
    image: string;
    user: {
      id: string;
      code: string;
      email: string;
      phone_code: string;
      phone_number: string;
    };
  };
  account: {
    full_name: string | null;
    image: string | null;
    user: {
      code: string;
      email: string;
      phone_code: string | null;
      phone_number: string | null;
      _count: {
        chats: number;
      };
    };
  } | null;
}
export interface UpdateListingDto {
  price?: number;
  status?: string;
}

export interface ListingSeekersImageDto {
  is_highlight: boolean;
  image: string;
}
export interface ListingSeekersDto {
  _count: {
    favorites: number;
  };
  code: string;
  title: string;
  excerpt: string | null;
  location: {
    province: string;
    city: string;
    district: string;
    postal_code: string;
    latitude: number;
    longitude: number;
  };
  account: {
    first_name: string;
    last_name: string;
  };
  images: ListingSeekersImageDto[];
  availability: {
    updated_at: string;
    price: number;
    type: ItemWithDetailDto;
    available_at?: string;
    duration_max: number | null;
    duration_min: number | null;
    duration_max_unit: ItemWithSuffixDto | null;
    duration_min_unit: ItemWithSuffixDto | null;
    is_negotiable?: boolean;
  };
  detail: {
    casco_status: boolean;
    land_size: number;
    building_size: number;
    garden_size: number;
    bathroom_total: ItemWithSuffixDto;
    bedroom_total: ItemWithSuffixDto;
    option: {
      name: string;
      type: string;
    };
  };
  features: {
    selling_points: {
      id: string;
      title: string;
      value: string;
      suffix: string | null;
    }[];
  };
  status: string;
}
export interface GetSeekersListingDto {
  location?: string;
  category?: string[];
  section?: string;
  limit?: number;
}

export interface PostFavoritePropertyDto {
  code: string;
  is_favorite: boolean;
}
export interface AreaDto {
  latitude?: string;
  longitude?: string;
  zoom?: string;
}

export interface GetFilteredSeekersListingDto {
  search?: string;
  type?: string[];
  min_price?: number;
  max_price?: number;
  land_largest?: number;
  land_smallest?: number;
  building_largest?: number;
  building_smallest?: number;
  garden_largest?: number;
  garden_smallest?: number;
  years_of_building?: string;
  bedroom_total?: number;
  bathroom_total?: number;
  start_date?: string;
  end_date?: string;
  selling_points?: string[];
  rental_offers?: string[];
  parking_option?: string;
  pool_option?: string;
  living_option?: string;
  furnishing_option?: string;
  page: string;
  per_page: string;
  area?: AreaDto;
  sort_by?: string;
  features?: string[];
  electricity?: string;
  property_of_view?: string[];
  location_type?: string;
  contract_duration?: string;
}
export interface PutSaveFilterListingDto {
  filters: GetFilteredSeekersListingDto[];
}

export interface GetLocationSuggestionDto {
  search: string;
}

export interface LocationSuggestionDto {
  province?: string;
  city?: string;
  district?: string;
}

export interface FilterOptionChildren {
  id: string;
  title: string;
  value: string;
}
export interface FilterOptions {
  id: string;
  name: string;
  description: string;
  childrens: FilterOptionChildren[];
}

export interface GetFilterParameterDto {
  price_range: {
    _max: {
      price: number;
    };
    _min: {
      price: number;
    };
  };
  size_range: {
    _max: {
      land_size: number;
      building_size: number;
      garden_size: number;
    };
    _min: {
      land_size: number;
      building_size: number;
      garden_size: number;
    };
  };
  parking_options: FilterOptions[];
  pool_options: FilterOptions[];
  living_options: FilterOptions[];
  furnishing_options: FilterOptions[];
}

export interface GetHomePageSeekersDto {
  newest: ListingSeekersDto[];
  popular: ListingSeekersDto[];
  featured: ListingSeekersDto[];
  commercial: ListingSeekersDto[];
}

export interface GetBatchPropertyList {
  property_list: string[];
}
