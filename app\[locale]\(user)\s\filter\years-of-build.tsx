import { useSeekerFilterStore } from "@/stores/seekers-filter-2.store"
import FilterContentLayout from "./filter-content-layout"
import { BaseSelectInputValue } from "@/types/base"
import { cn } from "@/lib/utils"
import CheckboxFilterItem from "./checkbox-filter-item"
import { useTranslations } from "next-intl"
import moment from "moment"

export default function YearsOfBuild() {
  const t = useTranslations("seeker")
  const { yearsOfBuild, setYearsOfBuild } = useSeekerFilterStore(state => state)
  const contractDruration: BaseSelectInputValue<string>[] = [
    {
      id: "0",
      content: t('listing.filter.yearsOfBuild.optionAny.title'),
      value: "ANY"
    },
    {
      id: "1",
      content: t('listing.filter.yearsOfBuild.optionOne.title'),
      value: "1800_2015"
    },
    {
      id: "2",
      content: t('listing.filter.yearsOfBuild.optionTwo.title'),
      value: "2016_2019"
    },
    {
      id: "3",
      content: t('listing.filter.yearsOfBuild.optionThree.title'),
      value: "2020_2024"
    },
    {
      id: "4",
      content: t('listing.filter.yearsOfBuild.optionFour.title'),
      value: moment().format("YYYY").toString()
    },

  ]
  return <FilterContentLayout title={t('listing.filter.others.yearsOfBuild.title')}>
    <div className="flex gap-2 max-sm:flex-wrap">
      {contractDruration.map(item => <CheckboxFilterItem
        key={item.id}
        item={item}
        setValue={setYearsOfBuild}
        isActive={yearsOfBuild == item.value}
        className={cn(
          "max-sm:!w-fit !w-full text-center items-center justify-center"
        )}
      />
      )}
    </div>
  </FilterContentLayout>
}