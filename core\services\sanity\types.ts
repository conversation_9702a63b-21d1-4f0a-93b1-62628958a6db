import { PortableTextBlock } from "sanity";

export type Author = {
  name: string;
  image: {
    asset: {
      url: string;
    };
  };
  bio?: string;
  slug: {
    current: string;
  };
  _id?: number | string;
  _ref?: number | string;
};

export type Blog = {
  _id: number;
  title: string;
  slug: any;
  metadata: string;
  body: PortableTextBlock[];
  mainImage: any;
  author: Author;
  tags: string[];
  publishedAt: string;
  category: {
    _id: string;
    title: string;
  };
};

export type SeoContent = {
  title: string;
  body: PortableTextBlock[];
};

export type DefaultContent = {
  title: string;
  body: PortableTextBlock[];
};
