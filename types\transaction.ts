import { TopUpBalance } from "@/core/domain/transaction/balance";
import { BaseCurrency } from "./base";

export type DiscountType = "percentage" | "fix-price" | "";

export interface TopUpBalanceProps {
  onClick: (credit:number, totalPrice:number) => void;
  currency?: BaseCurrency;
  data:TopUpBalance
}

export interface TopUpBalancePropsNew {
  totalBalance?: number;
  price?: number;
  discount?: number;
  typeDiscount?: DiscountType;
  onClick?: () => void;
  currency?: BaseCurrency;
  data: TopUpBalanceCardProps[];
}

export interface TopUpBalanceCardProps {
  totalBalance?: number;
  price: number;
  discount?: number;
  typeDiscount: DiscountType;
  currency: BaseCurrency;
  onClick?: () => void;
  priceAfterDiscount?: number;
}
// export interface TopUpBalanceCardProps {
//   totalBalance: number;
//   price: number;
//   discount?: number;
//   typeDiscount: DiscountType;
//   onClick?: () => void;
//   currency: BaseCurrency;
// }
