import { create } from "zustand";

interface CheckoutState {
  price:number,
  code?:string,
  discount?:string,
  discountedPrice:string,
  setPrice: (val:number) => void, 
  setDiscount: (val:string) => void, 
  setDiscountedPrice: (val:string) => void,
  setCode:(val:string) => void
}

export const useCheckoutStore = create<CheckoutState>(set => ({
  price: 0,
  discount: undefined,
  discountedPrice: "",
  code: "",
  setPrice: (price) => set({price}),
  setDiscount: (discount) => set({discount}),
  setDiscountedPrice: (discountedPrice) => set({discountedPrice}),
  setCode: (code) => set({code})
}))