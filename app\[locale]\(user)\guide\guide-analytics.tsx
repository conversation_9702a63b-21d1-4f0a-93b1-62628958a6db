"use client"

import { useEffect, useRef } from 'react';

export default function GuideAnalytics() {
  const startTime = useRef<number>(Date.now());
  const scrollDepthTracked = useRef<Set<number>>(new Set());
  const timeOnPageTracked = useRef<Set<number>>(new Set());

  useEffect(() => {
    // Track page view
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'page_view', {
        page_title: 'Bali Housing Guide Landing Page',
        page_location: window.location.href,
        event_category: 'Lead Magnet',
      });
    }

    // Scroll depth tracking
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercentage = Math.round((scrollTop / documentHeight) * 100);

      // Track scroll milestones: 25%, 50%, 75%, 90%
      const milestones = [25, 50, 75, 90];
      milestones.forEach(milestone => {
        if (scrollPercentage >= milestone && !scrollDepthTracked.current.has(milestone)) {
          scrollDepthTracked.current.add(milestone);
          
          if (typeof window !== 'undefined' && window.gtag) {
            window.gtag('event', 'scroll_depth', {
              event_category: 'Lead Magnet',
              event_label: `${milestone}%`,
              value: milestone,
              custom_parameter_1: 'scroll_milestone'
            });
          }
        }
      });
    };

    // Time on page tracking
    const trackTimeOnPage = () => {
      const currentTime = Date.now();
      const timeSpent = Math.round((currentTime - startTime.current) / 1000); // in seconds

      // Track time milestones: 30s, 60s, 120s, 300s (5min)
      const timeMilestones = [30, 60, 120, 300];
      timeMilestones.forEach(milestone => {
        if (timeSpent >= milestone && !timeOnPageTracked.current.has(milestone)) {
          timeOnPageTracked.current.add(milestone);
          
          if (typeof window !== 'undefined' && window.gtag) {
            window.gtag('event', 'time_on_page', {
              event_category: 'Lead Magnet',
              event_label: `${milestone}s`,
              value: milestone,
              custom_parameter_1: 'time_milestone'
            });
          }
        }
      });
    };

    // Set up event listeners
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Track time every 10 seconds
    const timeInterval = setInterval(trackTimeOnPage, 10000);

    // Track when user leaves the page
    const handleBeforeUnload = () => {
      const finalTime = Math.round((Date.now() - startTime.current) / 1000);
      
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'page_exit', {
          event_category: 'Lead Magnet',
          event_label: 'Page Exit',
          value: finalTime,
          custom_parameter_1: 'total_time_spent'
        });
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // Track visibility changes (tab switching)
    const handleVisibilityChange = () => {
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'visibility_change', {
          event_category: 'Lead Magnet',
          event_label: document.hidden ? 'Hidden' : 'Visible',
          value: document.hidden ? 0 : 1
        });
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearInterval(timeInterval);
    };
  }, []);

  // Track section views using Intersection Observer
  useEffect(() => {
    const observerOptions = {
      threshold: 0.5, // Trigger when 50% of element is visible
      rootMargin: '0px'
    };

    const handleIntersection = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionName = entry.target.getAttribute('data-section');
          
          if (sectionName && typeof window !== 'undefined' && window.gtag) {
            window.gtag('event', 'section_view', {
              event_category: 'Lead Magnet',
              event_label: sectionName,
              value: 1
            });
          }
        }
      });
    };

    const observer = new IntersectionObserver(handleIntersection, observerOptions);

    // Observe all sections with data-section attribute
    const sections = document.querySelectorAll('[data-section]');
    sections.forEach(section => observer.observe(section));

    return () => {
      sections.forEach(section => observer.unobserve(section));
    };
  }, []);

  return null; // This component doesn't render anything
}
