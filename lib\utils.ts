import { Role } from "@/types/base";
import { type ClassValue, clsx } from "clsx";
import moment from "moment";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const checkDomain = (domain: string): Role =>
  domain == process.env.ADMIN_DOMAIN ? "OWNER" : "SEEKER";

export const removeLocaleFromUrl = (url: string): string =>
  url.replace(/^\/[a-z]{2}(\/|$)/, "/");

export const checkIfSameUrl = (
  originalUrl: string,
  compareUrl: string
): boolean => removeLocaleFromUrl(originalUrl).includes(compareUrl);

const currencytoCorrespondingLocale = (currency: string) => {
  switch (currency) {
    case "USD":
      return "en-us";
    case "IDR":
      return "id-ID";
    case "GBP":
      return "en-GB";
    case "AUD":
      return "en-AU";
    case "EUR":
      return "nl-NL";
    default:
      return "en-us";
  }
};
export const formatCurrency = (
  value: number,
  currency = "USD",
  locale = "en-US"
) => {
  return new Intl.NumberFormat(currencytoCorrespondingLocale(currency), {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: currency == "IDR" ? 0 : 2,
  }).format(value);
};

export const formatNumber = (value: number, locale = "en-US") =>
  new Intl.NumberFormat(locale, {
    style: "decimal",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);

export const matchesBaseRoute = (baseRouteList: string[], url: string) =>
  baseRouteList.some((item) => url.startsWith(item));

export function setCookie(name: string, value: string, days: number) {
  let expires = "";
  if (days) {
    const date = new Date();
    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
    expires = "; expires=" + date.toUTCString();
  }
  document.cookie = name + "=" + (value || "") + expires + "; path=/";
}
export function removeCookie(name: string) {
  document.cookie = name + "=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
}

export function checkIfEmail(value: string) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  return emailRegex.test(value);
}

export function timeFormatter(time: string) {
  const date = moment(time);
  const today = moment();
  if (date.isSame(today, "day")) {
    return date.format("HH:mm");
  } else {
    return date.format("DD/MM/YY");
  }
}

export function slugGenerator(val: string) {
  const text = val.replaceAll(/[^a-zA-Z0-9\s]/g, "-");
  const textRemoveSpacing = text.replaceAll(/\s/g, "--");
  return textRemoveSpacing;
}

export const toggleStringArrrayItem = (values: string[], value: string) =>
  values.includes(value)
    ? values.filter((item) => item !== value)
    : [...values, value];

export const isSameArray = (arr1: any[], arr2: any[]) => {
  return (
    new Set(arr1).size === new Set(arr2).size &&
    arr1.every((item) => arr2.includes(item))
  );
};

export function arraysShareValue(arr1: any[], arr2: any[]) {
  return arr1.some((value) => arr2.includes(value));
}

// function to create sentence text (capitalize first letter in the whole sentence)
export const uppercaseFirstLetter = (val: string) =>
  val.charAt(0).toUpperCase() + val.slice(1);

export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_ID;
export const pageViewGtag = (url: string) => {
  window.gtag("config", GA_TRACKING_ID, { page_path: url });
};

export const gtagEvent = (event: {
  action: string;
  category: string;
  label: string;
  value: string;
}) => {
  if (typeof window !== "undefined" && window.gtag) {
    window.gtag("event", event.action, {
      event_category: event.category,
      event_label: event.label,
      value: event.value,
    });
  }
};
