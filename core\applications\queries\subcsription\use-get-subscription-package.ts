import { getAllSubscriptionPackagesService } from "@/core/infrastructures/subscription/service"
import { useQuery } from "@tanstack/react-query"


export const SUBSCRIPTION_PACKAGES_QUERY_KEY = "subscription-packages"
export function useGetSubscriptionPackage(){
  const query = useQuery({
    queryKey: [SUBSCRIPTION_PACKAGES_QUERY_KEY],
    queryFn: async () => await getAllSubscriptionPackagesService()
  })
  return query
}