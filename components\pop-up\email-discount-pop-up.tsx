"use client"

import { useEffect, useState } from "react"
import DialogWrapper from "../dialog-wrapper/dialog-wrapper"
import EmailInputDiscountForm from "./email-discount-pop-up/email-input-discount.form"
import { useMotionValueEvent, useScroll } from "framer-motion"
import { useUserStore } from "@/stores/user.store"
import { useLocale, useTranslations } from "next-intl"
import { Button } from "../ui/button"
import Link from "next/link"
import { noLoginPlanUrl } from "@/lib/constanta/route"
import { useToast } from "@/hooks/use-toast"
import PopUpBackground from "@/public/pop-up-background.jpeg"
import Image from "next/image"
import { X, Gift } from "lucide-react"

const DISCOUNT_CODE = "WELCOME25"
const DISCOUNT_PERCENTAGE = 25
const SCROLL_THRESHOLD = 0.3 // 30% scroll

export default function EmailDiscountPopup() {
  const t = useTranslations("seeker")
  const locale = useLocale()
  const [open, setOpen] = useState(false)
  const [isValidScroll, setIsValidScroll] = useState(false)
  const { scrollYProgress } = useScroll()
  const { seekers, hydrated } = useUserStore()
  const { toast } = useToast()
  const [isSubmittedEmail, setSubmittedEmail] = useState(false)

  // Show popup on scroll threshold
  useMotionValueEvent(scrollYProgress, "change", (latest) => {
    if (latest > SCROLL_THRESHOLD) {
      setIsValidScroll(true)
    }
  })

  useEffect(() => {
    if (!hydrated) return
    if (seekers.email) return
    if (isValidScroll) {
      const timer = setTimeout(() => setOpen(true), 500)
      return () => clearTimeout(timer)
    }
  }, [hydrated, seekers.email, isValidScroll])

  return (
    <DialogWrapper
      open={open}
      setOpen={setOpen}
      openTrigger={<></>}
      dialogClassName="overflow-hidden max-w-4xl w-full md:min-w-[800px] p-0 [&>button]:hidden"
      drawerClassName="overflow-hidden"
      dialogOverlayClassName="bg-black/10"
    >
      {/* Close button */}
      <Button
        variant="ghost"
        size="icon"
        className="absolute top-4 right-4 z-30 text-gray-600 hover:bg-gray-100 rounded-full"
        onClick={() => setOpen(false)}
      >
        <X className="h-4 w-4" />
      </Button>

      <div className="flex flex-col md:flex-row min-h-[500px]">
        {/* Left Side - Image */}
        <div className="relative md:w-1/2 h-64 md:h-auto">
          <Image
            src={PopUpBackground}
            alt="Bali Property"
            className="object-cover w-full h-full"
            fill
          />
          <div className="absolute inset-0 bg-black/30"></div>
        </div>

        {/* Right Side - Content */}
        <div className="md:w-1/2 p-8 bg-white flex flex-col">
          <div className="flex-1 flex flex-col justify-center">
            {isSubmittedEmail ? (
              /* Success state */
              <div className="text-center space-y-4">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <Gift className="h-8 w-8 text-green-600" />
                </div>

                <h4 className="text-lg font-bold text-seekers-text">
                  {t('promotion.popUp.successState.title')}
                </h4>

                <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-4">
                  <p className="text-sm text-gray-600 mb-2">
                    {t('promotion.popUp.successState.description')}
                  </p>
                  <div className="bg-seekers-primary text-white px-4 py-2 rounded-lg font-mono text-lg font-bold tracking-wider">
                    {DISCOUNT_CODE}
                  </div>
                </div>

                <p className="text-sm text-seekers-text-light">
                  {t.rich("promotion.popUp.couponCodeDescription", {
                    code: () => (
                      <span className="font-bold text-seekers-primary">
                        {DISCOUNT_CODE}
                      </span>
                    ),
                  })}
                </p>

                <Button
                  asChild
                  className="w-full"
                  onClick={() => {
                    navigator.clipboard.writeText(DISCOUNT_CODE)
                    toast({
                      title: t("misc.copy.successCopyContent", {
                        content: t("misc.promoCode"),
                      }),
                    })
                  }}
                >
                  <Link href={noLoginPlanUrl} hrefLang={locale}>
                    {t("cta.useDiscountCode")}
                  </Link>
                </Button>
              </div>
            ) : (
              /* Email capture state */
              <div className="space-y-6">
                <div className="text-center space-y-3">
                  <h3 className="text-2xl font-bold text-seekers-text leading-tight">
                    {t("promotion.popUp.title")}
                  </h3>
                  <p className="text-seekers-text-light">
                    {t("promotion.popUp.description", {
                      count: DISCOUNT_PERCENTAGE,
                    })}
                  </p>
                  <p className="text-seekers-text-light">{t('promotion.popUp.footnote')}</p>
                </div>

                <EmailInputDiscountForm setIsSubmitted={setSubmittedEmail} />

                <div className="text-center">
                  <Button
                    variant="link"
                    className="text-sm text-gray-500 hover:text-gray-700"
                    onClick={() => setOpen(false)}
                  >
                    {t("misc.maybeLater")}
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Terms & Conditions pinned to bottom */}
          <div className="mt-auto pt-6 text-center">
            <p className="text-xs text-seekers-text-light leading-relaxed">
              {t.rich("promotion.popUp.termsAndCondition", {
                term: (chunk) => (
                  <span className="font-bold text-seekers-text underline cursor-pointer">
                    {chunk}
                  </span>
                ),
                privacy: (chunk) => (
                  <span className="font-bold text-seekers-text underline cursor-pointer">
                    {chunk}
                  </span>
                ),
              })}
            </p>
          </div>
        </div>
      </div>
    </DialogWrapper>
  )
}
