"use client"

import { usePathname, useSearchParams } from "next/navigation"
import { useEffect } from "react"

const KEY = process.env.NEXT_PUBLIC_PIXEL_KEY!
export default function FacebookPixel() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  useEffect(() => {
    import("react-facebook-pixel")
      .then((x) => x.default)
      .then((ReactPixel) => {
        ReactPixel.init(KEY); //don't forget to change this
        ReactPixel.pageView();
        ReactPixel.track("open website", {
          pathname: pathname,
          property_detail: searchParams.get("code") ? {
            title: pathname,
            code: searchParams.get("code")
          } : null
        })
      });
  }, [pathname, searchParams])
  return <></>
}