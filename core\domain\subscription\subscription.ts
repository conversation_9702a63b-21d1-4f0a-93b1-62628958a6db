export interface Subscription {
  name: string;
  productId: string;
  currency: string;
  priceVariant: SubscriptionPricingDetail[];
}

export interface SubscriptionPricingDetail {
  priceId: string;
  price: number;
  cycleUnit: string;
  cycleCount: number;
  currency: string;
}
export const packages = {
  archiver: "Achiever",
  finder: "Finder",
  free: "Free",
} as const;

export type PackagesType = (typeof packages)[keyof typeof packages];

export const packageFeature = {
  contactOwner: "contact-owner",
  photos: "photos",
  mapLocation: "map-location",
  advanceAndSaveFilter: "advance-and-save-filter",
  savedListing: "saved-listings",
  // listingUpdate: "listing-update",
  // priceHistory: "price-history",
  // virtualTour: "virtual-tour",
  // neighborhoodInsights: "neighborhodd-insights",
  // comparisonTool: "comparison-tool",
  // expertConsultant: "expert-consultant",
  // offMarketListing: "off-market-listing",
  // transactions: "transactions",
  favoriteProperties: "favorite-properties",
} as const;

export type PackageFeatureType =
  (typeof packageFeature)[keyof typeof packageFeature];

export type PackageFeatureDetailType = {
  [key in PackageFeatureType]: boolean | string;
};

export const getProductId = (
  packageName: PackagesType,
  subscriptionPackage: Subscription[]
) => {
  const data = subscriptionPackage.find((item) => packageName == item.name);
  return data?.productId || "";
};
