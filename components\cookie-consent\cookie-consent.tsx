"use client"
import { ANALYTIC_COOKIES_COLLECTION, COOKIES_COLLECTION_STATUS, FUNCTIONAL_COOKIES_COLLECTION, MARKETING_COOKIES_COLLECTION, NECESSARY_COOKIES_COLLECTION } from "@/lib/constanta/constant"
import * as m from "framer-motion/m"

import { AnimatePresence } from "framer-motion"
import Cookies from "js-cookie"
import { CookieIcon } from "lucide-react"
import { Button } from "../ui/button"
import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"
import { Switch } from "../ui/switch"
import { LazyMotion, domAnimation } from "framer-motion"

export default function CookieConsent() {
  const pathName = usePathname()
  const hasAcceptedCookies = Cookies.get(COOKIES_COLLECTION_STATUS)
  const [openCookiesConsent, setOpenCookiesConsent] = useState(false)
  const [openDetail, setOpenDetail] = useState(false)
  const [cookiesCollection, setCookiesCollection] = useState({
    necessary: true,
    functional: false,
    analytic: false,
    marketing: false
  })

  const handleAcceptingCookies = (
    acceptingValueType: "all" | "necessary" | "custom") => {

    if (!["all", "necessary", "custom"].includes(acceptingValueType)) return
    Cookies.set(COOKIES_COLLECTION_STATUS, 'true')

    if (acceptingValueType == "necessary") {
      setCookiesCollection(prev => ({ ...prev, necessary: true }))

    } else if (acceptingValueType == "all") {
      setCookiesCollection(prev => ({
        analytic: true,
        functional: true,
        marketing: true,
        necessary: true
      }))
    }
    Cookies.set(NECESSARY_COOKIES_COLLECTION, cookiesCollection.necessary.toString())
    Cookies.set(FUNCTIONAL_COOKIES_COLLECTION, cookiesCollection.functional.toString())
    Cookies.set(ANALYTIC_COOKIES_COLLECTION, cookiesCollection.analytic.toString())
    Cookies.set(MARKETING_COOKIES_COLLECTION, cookiesCollection.marketing.toString())
    setOpenCookiesConsent(false)
  }
  useEffect(() => {
    if (hasAcceptedCookies) {
      setOpenCookiesConsent(false)
    } else {
      setOpenCookiesConsent(true)
    }
  }, [hasAcceptedCookies])
  return <LazyMotion features={domAnimation}>
    <AnimatePresence>

      {(openCookiesConsent && (!pathName.includes("create-password") || !pathName.includes("reset-password"))) ?
        <m.div
          initial={{
            y: 20,
            opacity: 0
          }}
          animate={{
            y: 0,
            opacity: 1
          }}
          exit={{
            y: 20,
            opacity: 0
          }}
          transition={{
            duration: 0.7,
            ease: "easeInOut"
          }}
          className="fixed max-w-sm w-full bottom-4 left-4 bg-background p-8 z-20 shadow-md rounded-2xl space-y-4">
          <p className="inline-flex items-center gap-2 font-semibold">
            <span>
              <CookieIcon />
            </span>
            Manage Cookie Preferences
          </p>
          {openDetail ?
            <section className="space-y-4">
              <CookiesItem
                title="Necessary"
                onValueChange={(val) => setCookiesCollection(prev => ({ ...prev, necessary: val }))}
                description="These cookies are essential for the website to function properly. They enable core functionalities such as security, network management, and accessibility. You cannot disable these cookies."
                value={cookiesCollection.necessary}
                disabled
              />
              <CookiesItem
                title="Analytics"
                onValueChange={(val) => setCookiesCollection(prev => ({ ...prev, analytic: val }))}
                description="These cookies allow the website to remember your preferences and provide enhanced features like saved language settings or embedded videos."
                value={cookiesCollection.analytic}
              />
              <CookiesItem
                title="Functional"
                onValueChange={(val) => setCookiesCollection(prev => ({ ...prev, functional: val }))}
                description="These cookies are essential for the website to function properly. They enable core functionalities such as security, network management, and accessibility. You cannot disable these cookies."
                value={cookiesCollection.functional}
              />
              <CookiesItem
                title="Marketing"
                onValueChange={(val) => setCookiesCollection(prev => ({ ...prev, marketing: val }))}
                description="These cookies are used to deliver relevant ads and track your activity across websites to personalize your advertising experience."
                value={cookiesCollection.marketing}
              />
            </section>
            :
            <section>
              <p className="text-seekers-text-light">We use cookies to optimize your experience on our website. You can choose which categories of cookies to allow below.</p>
            </section>
          }
          <div className="inline-flex gap-2">
            <Button onClick={() => handleAcceptingCookies("all")}>Accept all</Button>
            {
              openDetail ?
                <Button variant={"ghost"} onClick={() => handleAcceptingCookies("custom")}>
                  Save preferences
                </Button>
                :
                <Button variant={"ghost"} onClick={() => setOpenDetail(true)}>Manage preferences</Button>
            }
          </div>
        </m.div>
        : null
      }
    </AnimatePresence>
  </LazyMotion>
}

function CookiesItem({ title, value, disabled, description, onValueChange }: { title: string, value: boolean, disabled?: boolean, description: string, onValueChange: (val: boolean) => void }) {
  return <div className="space-y-2">
    <div className="flex justify-between">
      <p className="font-semibold">{title}</p>
      <Switch checked={value} disabled={disabled} onCheckedChange={val => onValueChange(val)} />
    </div>
    <p className="text-xs">{description}</p>
  </div>
}