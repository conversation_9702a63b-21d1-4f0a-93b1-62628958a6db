import { useTranslations } from "next-intl"

export default function TooManyRequestError() {
  const t = useTranslations('seeker')
  return <>
    <div className='min-h-[80vh] flex justify-center items-center'>
      <div className='space-y-4 text-center flex flex-col items-center'>
        <h1 className='text-2xl text-seekers-text font-bold'>{t('misc.error.tooManyRequest.title')}</h1>
        <p className='tex'>{t('misc.error.tooManyRequest.description')}</p>
      </div>
    </div>
  </>
}