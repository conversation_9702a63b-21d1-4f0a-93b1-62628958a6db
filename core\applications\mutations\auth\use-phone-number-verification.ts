import { sendWhatsappVerification } from "@/core/infrastructures/auth";
import { SendWhatsappVerificationDto } from "@/core/infrastructures/auth/dto";
import { useToast } from "@/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

export function useWhatsappVerification(onSuccess:() => void){
  const {toast} = useToast()
  const t = useTranslations()
  const mutation = useMutation({
    mutationFn: (data:SendWhatsappVerificationDto) => sendWhatsappVerification(data),
    onSuccess: (response) => {
      onSuccess()
    },
    onError: (error) => {
      const data: any = (error as any).response.data
      
      if(data.message.include(t('misc.verificationAlreadySent'))){
        onSuccess()
      }
      toast({
        title: t('misc.foundError'),
        description: data.message,
        variant: "destructive"
      })
    },
  })
  return mutation
}