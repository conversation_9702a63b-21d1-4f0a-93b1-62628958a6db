"use client"
import { Carousel, CarouselContent, Carousel<PERSON>tem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { ListingListSeekers } from "@/core/domain/listing/listing-seekers";
import ListingItem from "../../(listings)/listing-item";

export default function PropertyRecommendationContent({ properties, conversions }: { properties: ListingListSeekers[], conversions: { [key: string]: number } }) {
  return <Carousel opts={{
    align: "end"
  }}>
    <CarouselContent className="w-full h-full -ml-4 -z-20">
      {properties.map((item, idx) => <CarouselItem key={idx} className="basis-1/2 lg:basis-1/3 ">
        <ListingItem key={idx} data={item} conversion={conversions} forceLazyloading maxImage={3} />
      </CarouselItem>
      )}
    </CarouselContent>
    <div className="flex absolute 
        top-[128px] max-sm:-translate-y-1/2  max-sm:left-0 
        w-full justify-between px-3 max-sm:hidden lg:hidden"
    >
      <CarouselPrevious onClick={e => e.stopPropagation()} className="-left-1.5 md:-left-3 transition duration-75 ease-in w-8 h-8 md:w-10 md:h-10 !bg-white/70 border-white-70" iconClassName="w-6" />
      <CarouselNext onClick={e => e.stopPropagation()} className="-right-1.5 md:-right-3 transition duration-75 ease-in w-8 h-8 md:w-10 md:h-10 !bg-white/70 border-white-70" iconClassName="w-6" />
    </div>
  </Carousel>
}