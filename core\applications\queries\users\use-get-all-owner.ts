import { getAllOwnerService } from "@/core/infrastructures/user/services";
import { BasePaginationRequest } from "@/types/base";
import { useQuery } from "@tanstack/react-query";

export const ALL_OWNER_QUERY_KEY = "all-owner"
export function useGetAllOwner(data:BasePaginationRequest){
  const {
    page,
    per_page,
    search
  } = data
  const query = useQuery({
    queryKey: [ALL_OWNER_QUERY_KEY,page,per_page,search],
    queryFn: async () => {
      const data:BasePaginationRequest = {
        page,per_page,search
      } 
      return await getAllOwnerService(data)
    },
    retry: 0
  },
)
  return query
}