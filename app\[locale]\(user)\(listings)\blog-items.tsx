"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { imagePlaceholder } from "@/lib/constanta/image-placeholder"
import { Link } from "@/lib/locale/routing"
import { cn } from "@/lib/utils"
import { useLocale, useTranslations } from "next-intl"
import Image from "next/image"
import { useRouter } from "nextjs-toploader/app"
import { ComponentProps } from "react"

export interface BlogItemProps extends ComponentProps<"div"> {
  title: string
  date: string
  url: string
  content: string
  image: string
}

export default function BlogItem({
  title,
  date,
  image,
  content,
  url,
  ...rest
}: BlogItemProps) {
  const t = useTranslations("seeker")
  const route = useRouter()
  const locale = useLocale()
  return (
    <div onClick={() => route.push(url)}
      {...rest}
      className={cn(
        "w-full grid grid-cols-1 cursor-pointer md:grid-cols-[300px_1fr] gap-6",
        rest.className
      )}
    >
      <div className="relative aspect-[4/3] w-full overflow-hidden rounded-xl">
        <Image
          src={image}
          alt={title}
          title={title}
          fill
          className="object-cover"
          loading="lazy"
          placeholder="blur"
          blurDataURL={imagePlaceholder}
        />
      </div>
      <div className="flex flex-col justify-between space-y-3">
        <div className="space-y-2">
          <h3 className="font-bold line-clamp-2 text-seekers-text text-base">
            {title}
          </h3>
          <p className="text-seekers-text-light line-clamp-6">
            {content}
          </p>
        </div>
        <Button
          asChild
          variant="link"
          className="!p-0 h-fit w-fit text-sm font-medium text-seekers-text-light"
        >
          <Link href={url} hrefLang={locale} className="text-xs font-medium !gap-0.5">
            {t('cta.readMore')}
          </Link>
        </Button>
      </div>
    </div>
  )
}