import { useLogout } from "@/core/applications/mutations/auth/use-logout"
import { useTranslations } from "next-intl"
import DialogWrapper from "../dialog-wrapper/dialog-wrapper"
import DialogHeaderWrapper from "../dialog-wrapper/dialog-header-wrapper"
import { DialogFooter } from "../ui/dialog"
import { Button } from "../ui/button"
import { useState } from "react"
import Cookies from "js-cookie"
import { ACCESS_TOKEN } from "@/lib/constanta/constant"

export default function LogoutDialog({ trigger }: { trigger: React.ReactNode }) {
  const [open, setOpen] = useState(false)
  const logoutQuery = useLogout("seekers")
  const t = useTranslations('seeker')
  const handleLogout = () => {
    if (!Cookies.get(ACCESS_TOKEN)) {
      window.location.assign('')
      return
    } else {
      logoutQuery.mutate()
    }
  }
  return <DialogWrapper
    open={open}
    setOpen={setOpen}
    openTrigger={
      trigger
    }
    dialogClassName="max-w-md"
  >
    <DialogHeaderWrapper className="text-start px-0">
      <h2 className="max-sm:text-center font-semibold">
        {t('accountAndProfile.logout.title')}
      </h2>
      <p className="max-sm:text-center max-sm:mb-4">{t('owner.accountAndProfile.logout.description')}</p>
    </DialogHeaderWrapper>
    <DialogFooter>
      <Button
        variant={"default-seekers"}
        loading={logoutQuery.isPending}
        className="min-w-20 max-sm:order-last"
        onClick={() => setOpen(false)}
      >
        {t("cta.cancel")}
      </Button>
      <Button
        variant={"ghost"}
        onClick={handleLogout}
        loading={logoutQuery.isPending}
        className="min-w-20"
      >
        {t("cta.logout")}
      </Button>
    </DialogFooter>
  </DialogWrapper>
}