import DialogWrapper from "@/components/dialog-wrapper/dialog-wrapper";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { useState } from "react";
import EditBillingForm from "./edit-billing.form";
import { BillingInformation } from "@/core/domain/transaction/transaction";
import DialogHeaderWrapper from "@/components/dialog-wrapper/dialog-header-wrapper";
import DialogTitleWrapper from "@/components/dialog-wrapper/dialog-title-wrapper";

export default function EditBillingFormDialog({ billingInfo }: { billingInfo?: BillingInformation }) {
  const t = useTranslations("seeker")
  const [open, setOpen] = useState(false)
  return <DialogWrapper
    open={open}
    setOpen={setOpen}
    openTrigger={<Button variant="ghost" size="sm" className="text-seekers-primary hover:text-seekers-primary hover:bg-[#FAF6F0]">
      {t("cta.editBilling")}
    </Button>}
  >
    <DialogHeaderWrapper className="mb-2">
      <DialogTitleWrapper className="">Edit billing information</DialogTitleWrapper>
    </DialogHeaderWrapper>
    <EditBillingForm billingInfo={billingInfo} onSuccess={() => setOpen(false)} />
  </DialogWrapper>
}