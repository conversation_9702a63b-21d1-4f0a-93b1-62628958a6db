import { StepperItemId } from "@/lib/constanta/constant"
import { Stepper } from "@/types/listings"
import { useTranslations } from "next-intl"

export default function useStepperListing(){
  const t = useTranslations("owner")
  const StepperListingList: Stepper[] = [
  {
    id: StepperItemId.location,
    name: t('listing.stepper.location'),
    status: "partial",
  },
  {
    id: StepperItemId.basicInfo,
    name: t('listing.stepper.basicInformation'),
    status: "partial",
  },
  {
    id: StepperItemId.pricing,
    name: t('listing.stepper.pricingAndAvailability'),
    status: "partial",
  },
  {
    id: StepperItemId.features,
    name: t('listing.stepper.features'),
    status: "partial",
  },
  {
    id: StepperItemId.images,
    name: t('listing.stepper.images'),
    status: "partial",
  },

]
  return  StepperListingList
}