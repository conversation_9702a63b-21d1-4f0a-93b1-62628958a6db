import { apiClient } from "@/core/client";
import { PostSubscriptionDto, PostSubscriptionSignUpDto } from "./dto";
import ssrApiClient from "@/core/ssr-client";
import { fetchMethod } from "@/core/utils/types";
const baseUrl = process.env.NEXT_PUBLIC_SERVICE_API;

export const ssrGetAllSubscriptionPackages = async <T>() =>
  await ssrApiClient<T>(`${baseUrl}/packages/subscription`, fetchMethod.get, {
    next: { revalidate: 0 },
  });

export const postSubscriptionPackage = (data: PostSubscriptionDto) =>
  apiClient.post("/packages/subscription/checkout", data);

export const postUpdateSubscriptionPackage = (data: PostSubscriptionDto) =>
  apiClient.put("packages/subscription/update", data);

export const postCancelSubscriptionPackage = () =>
  apiClient.put("packages/subscription/cancel");

export const postSubscriptionSignUp = (data: PostSubscriptionSignUpDto) =>
  apiClient.post("packages/subscription/register", data);

export const getSubscriptionPackageDetail = async <T>(id: string) =>
  await ssrApiClient<T>(
    `${baseUrl}/packages/subcription/${id}`,
    fetchMethod.get,
    {
      next: { revalidate: 0, tags: [id] },
    }
  );
