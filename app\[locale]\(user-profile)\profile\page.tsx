import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import ProfileBreadCrumb from "./bread-crumb";
import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import { useTranslations } from "next-intl";
import ProfilePictureForm from "./form/profile-picture.form";
import ProfileForm from "./form/profile.form";
import { profileUrl } from "@/lib/constanta/route";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("seeker")
  return {
    title: t("metadata.profile.title"),
    description: t("metadata.profile.description"),
    alternates: {
      languages: {
        en: process.env.USER_DOMAIN + "/en" + profileUrl,
        id: process.env.USER_DOMAIN + "/id" + profileUrl
      }
    }
  }
}





export default function ProfilePage() {
  const t = useTranslations("seeker")
  return <>
    <ProfileBreadCrumb />
    <MainContentLayout className="space-y-8 my-8 max-sm:px-0">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">{t("setting.profile.personalInfo.title")}</h1>
        <h2 className="text-muted-foreground mt-2">{t("settings.profile.personalInfo.description")}</h2>
      </div>
      <ProfilePictureForm />
      <ProfileForm />
    </MainContentLayout>
  </>
}