import { useLocale, useTranslations } from "next-intl";
import MainContentLayout from "../seekers-content-layout/main-content-layout";
import Image from "next/image";
import Logo from "@/public/property-seekers-main-logo.png"
import { ComponentProps } from "react";
import { cn } from "@/lib/utils";
import { Link } from "@/lib/locale/routing";
import { Separator } from "../ui/separator";
import { FaFacebook, FaInstagram, FaLinkedin, FaTiktok } from "react-icons/fa";
import { contactUsUrl, privacySeekerUrl, termSeekerUrl, userhomepageUrl } from "@/lib/constanta/route";
import { listingCategory } from "@/core/domain/listing/listing-seekers";

export default function SeekersFooter() {
  const t = useTranslations("seeker")
  const locale = useLocale()
  const exploreProperties = [
    { id: "villas", url: `/s/all?t=${listingCategory.villa}`, content: t('footer.tabsOne.content.optionOne.title') },
    { id: "apartment", url: `/s/all?t=${listingCategory.apartment}`, content: t('footer.tabsOne.content.optionTwo.title') },
    { id: "guesthouse", url: `/s/all?t=${listingCategory.rooms}`, content: t('footer.tabsOne.content.optionThree.title') },
    { id: "homestay", url: `/s/all?t=${listingCategory.rooms}`, content: t('footer.tabsOne.content.optionFour.title') },
    { id: "shops", url: `/s/all?t=${listingCategory.shops}`, content: t('footer.tabsOne.content.optionFive.title') },
    { id: "offices", url: `/s/all?t=${listingCategory.offices}`, content: t('footer.tabsOne.content.optionSix.title') },
    { id: "restaurant", url: `/s/all?t=${listingCategory.cafeOrRestaurants}`, content: t('footer.tabsOne.content.optionSeven.title') },
  ];

  const propertiesOwner = [
    { id: "get-your-property-listed", url: process.env.ADMIN_DOMAIN || "", content: t('footer.tabsTwo.content.optionOne.title') },
    { id: "faq-for-owner", url: (process.env.ADMIN_DOMAIN || "") + "/#faq", content: t('footer.tabsTwo.content.optionTwo.title') },
  ];

  const help = [
    { id: "faq", url: `${userhomepageUrl}#faq`, content: t('footer.tabsFour.content.optionOne.title') },
    { id: "contact-support", url: contactUsUrl, content: t('footer.tabsFour.content.optionTwo.title') },
    { id: "about-us", url: "/about-us", content: t('footer.tabsFour.content.optionThree.title') },
    { id: "terms-of-use", url: termSeekerUrl, content: t('footer.tabsFour.content.optionFour.title'), target: "_blank" },
    { id: "privacy-policy", url: privacySeekerUrl, content: t('footer.tabsFour.content.optionFive.title'), target: "_blank" },

  ];

  return <>
    <footer className=" bg-seekers-foreground/50 space-y-8 w-full py-6">
      <MainContentLayout className="space-y-6">
        <div className="w-full grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-2 md:gap-x-6 gap-y-8  py-6">
          <div className="space-y-1  col-span-2 lg:col-span-4 xl:col-span-2 ">
            <div className="flex gap-2 items-center">
              <Image src={Logo} alt="Properti-Plaza" width={164} height={48} />
            </div>
            <p className="text-body-variant text-xs font-normal text-seekers-text-light leading-6 tracking-[0.06px]">
              {t('footer.slogan')}
            </p>
          </div>
          <div className="max-sm:hidden">
            {/* Keep this DIV empty */}
          </div>
          {/* Explore properties */}
          <LinkContent title={t('footer.exploreProperties.title')}>
            {exploreProperties.map(item => <Link hrefLang={locale} key={item.id} href={item.url}>
              {item.content}
            </Link>
            )}
          </LinkContent>
          {/* Property owner */}
          <LinkContent title={t('footer.properyOwner.title')}>
            {propertiesOwner.map(item => <Link hrefLang={locale} key={item.id} href={item.url}>
              {item.content}
            </Link>
            )}
          </LinkContent>

          {/* Help */}
          <LinkContent title={t('footer.help.title')}>
            {help.map(item => <Link key={item.id} hrefLang={locale} href={item.url} target={item.target || ""}>
              {item.content}
            </Link>
            )}
          </LinkContent>
        </div>
        <Separator />
        <div className="grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 items-center gap-2 md:gap-x-6">
          <p className="text-xs font-semibold text-seekers-text-light lg:col-span-3 xl:col-span-5">
            &copy; {t('footer.copyright')}
          </p>
          <div className="flex gap-4">
            <Link href={"https://www.facebook.com/join.propertyplaza"} hrefLang={locale} target="_blank" className="grid grid-flow-col auto-cols-max">
              <FaFacebook className="w-5 h-5" />
            </Link>
            <Link href={"https://www.instagram.com/join.propertyplaza/"} hrefLang={locale} target="_blank" className="grid grid-flow-col auto-cols-max">
              <FaInstagram className="w-5 h-5" />
            </Link>
            <Link href={"https://www.tiktok.com/@join.propertyplaza"} hrefLang={locale} target="_blank" className="grid grid-flow-col auto-cols-max">
              <FaTiktok className="w-5 h-5" />
            </Link>
            <Link href={"https://www.linkedin.com/company/property-plaza-indonesia"} hrefLang={locale} target="_blank" className="grid grid-flow-col auto-cols-max">
              <FaLinkedin className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </MainContentLayout>
    </footer>
  </>
}

interface LinkContentProps extends ComponentProps<"div"> {
  title: string,
  children: React.ReactNode
}


function LinkContent({ children, title, ...rest }: LinkContentProps) {
  return <div className={cn("space-y-4 w-48", rest)}>
    <h2 className="font-semibold  text-seekers-text">{title}</h2>
    <div className="flex flex-col gap-2 text-seekers-text-light text-xs">
      {children}
    </div>
  </div>
}