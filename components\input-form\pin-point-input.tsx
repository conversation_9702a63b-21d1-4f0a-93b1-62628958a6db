import { FormField } from '@/components/ui/form'
import { FieldValues } from 'react-hook-form'
import { Input } from '../ui/input'
import { BaseInputForm } from '@/types/base'
import BaseInputLayout from './base-input'

interface DefaultInputProps<T extends FieldValues> extends BaseInputForm<T> {
  label: string,
  placeholder: string
  description?: string
  type: string,
  onChange?: (value: string) => void,
  onBlur?: () => void
}
export default function PinPointInput<T extends FieldValues>({ form, label, name, placeholder, description, type, onChange, onBlur }: DefaultInputProps<T>) {
  return <FormField
    control={form.control}
    name={name}
    render={({ field, fieldState }) => (
      <BaseInputLayout label={label} description={description}>
        <div className='flex gap-2 w-full border rounded-sm focus-within:border-neutral-light'>
          <Input type={type} placeholder={placeholder} {...field} onChange={(e) => {
            field.onChange(e)
            onChange?.(e.target.value)
          }}
            onBlur={(e) => {
              field.onBlur()
              form.trigger(field.name)
              onBlur?.()
            }}
            className='border-none focus:outline-none shadow-none focus-visible:ring-0' />
        </div>
      </BaseInputLayout>
    )}
  />
}