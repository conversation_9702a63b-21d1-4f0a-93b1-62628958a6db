import { postFilteredSeekeresListings } from "@/core/infrastructures/listing/api";
import { GetFilteredSeekersListingDto } from "@/core/infrastructures/listing/dto";
import { useMutation } from "@tanstack/react-query";

export function usePostSeekersFilterListing(){
  const mutation = useMutation({
    mutationFn: (data:GetFilteredSeekersListingDto) => postFilteredSeekeresListings(data),
    onSuccess: response => response,
    retry: 0
  })
  return mutation
}