import React, { useEffect } from 'react'
import { FormField } from '@/components/ui/form'
import { FieldValues } from 'react-hook-form'
import { Input } from '../ui/input'
import { Button } from '../ui/button'
import { useState } from 'react'
import { Check, Pencil, X } from 'lucide-react'
import { BaseInputForm } from '@/types/base'
import BaseInputLayout from './base-input'
import { useSettingStore } from '@/stores/setting.store'
interface IndividualInputProps<T extends FieldValues> extends BaseInputForm<T> {
  label: string,
  placeholder: string
  description?: string
  onSubmit?: () => Promise<void>
  loading?: boolean,
  resetField?: () => void
}
export default function IndividualInput<T extends FieldValues>({ form, label, name, placeholder, description, onSubmit, loading, resetField }: IndividualInputProps<T>) {
  const [disabled, setDisabled] = useState<boolean>(true)
  const [isEdited, setIsEdited] = useState(false)
  const { setEditingStatus } = useSettingStore(state => state)

  return <FormField
    control={form.control}
    name={name}
    render={({ field }) => (
      <BaseInputLayout label={label} description={description}>
        <div className='flex gap-2 w-full border rounded-sm focus-within:border-neutral-light overflow-hidden'>
          <Input placeholder={placeholder} {...field} disabled={disabled} className='border-none focus:outline-none shadow-none focus-visible:ring-0 overflow-hidden' />
          {
            disabled ?
              <Button variant={"ghost"} className=' !rounded-none' onClick={() => {
                setDisabled(false)
                setEditingStatus(name, "add")
              }}>
                <Pencil className='w-3 h-3' />
              </Button>
              :
              <div className='flex'>
                <Button loading={loading} variant={"ghost"} className='text-red-500' type='reset' onClick={e => {
                  setDisabled(true)
                  resetField?.()
                  setEditingStatus(name, "remove")
                }} >
                  <X className='w-3 h-3' />
                </Button>
                <Button variant={"ghost"} className='text-green-500 rounded-r-none' onClick={async (e) => {
                  await onSubmit?.()
                  setDisabled(true)
                  setEditingStatus(name, "remove")
                }} type='submit' loading={loading}>
                  <Check className='w-3 h-3' />
                </Button>

              </div>
          }
        </div>
      </BaseInputLayout>
    )}
  />
}