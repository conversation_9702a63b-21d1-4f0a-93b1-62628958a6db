import { BasePaginationRequest } from "@/types/base";
import { getRoles } from "./api";
import {transformRole} from "./transform"
import { transformMeta } from "../utils/transform";
import { RoleDto } from "./dto";
export async function getAllRolesService(searchParam:BasePaginationRequest){

  try{
    const request = await getRoles(searchParam)
    const data:RoleDto[] = request.data.data.items as RoleDto[]
    const activeRoles = data.filter(item => item.is_active)
    return {
      data: transformRole(activeRoles), 
      meta:transformMeta(request.data.data.meta)
    }
  }catch(e){
    throw new Error(e as any)
  }
}