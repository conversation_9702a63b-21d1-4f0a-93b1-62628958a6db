import { getMessages } from 'next-intl/server';

// Function to safely load messages from a specific folder
async function loadMessages(folder: string, locale: string) {
  try {
    // Gebruik dynamic import in plaats van fs
    const messages = await import(`../../${folder}/${locale}.json`);
    return messages.default;
  } catch (error) {
    console.error(`Failed to load messages from ${folder}/${locale}.json:`, error);
    return {}; // Return een leeg object als het bestand niet bestaat
  }
}

// Function to merge messages from multiple sources
export async function getCombinedMessages(locale: string) {
  try {
    // Load messages from both directories
    const localeMessages = await loadMessages('locales', locale);
    const customMessages = await loadMessages('messages', locale);


    return { ...localeMessages, ...customMessages };
  } catch (error) {
    console.error('Error loading messages:', error);
    return {};
  }
} 