export interface PostSubscriptionDto {
  price_id: string;
  product_id: string;
}

export interface SubscriptionPackagesDto {
  name: string;
  ref_product_id: string;
  ref_price_id: string;
  price: number;
  currency: string;
  cycle_count: number;
  cycle_unit: string;
  price_unit: string;
  price_option: {
    price: number;
    cycle_unit: string;
    price_unit: string;
    cycle_count: number;
    currency: string;
    ref_price_id: string;
  }[];
}

export interface SubscriptionDetailPackageDto {
  id: string; // as Price ID
  product_id: string;
  name: string;
  price: number;
  currency: string;
  detail: {
    text: string;
    unit: string;
    value: number;
  };
}

export interface PostSubscriptionResponse {
  transaction: {
    code: string;
    created_at: string;
  };
  url: string;
}

export interface PostSubscriptionSignUpDto {
  last_name: string;
  first_name: string;
  email: string;
  product_id: string;
  price_id: string;
}
