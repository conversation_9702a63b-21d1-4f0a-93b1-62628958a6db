"use client"

import { <PERSON><PERSON> } from "@/components/ui/button";
import { gtagEvent } from "@/lib/utils";
import { ArrowDown, Download } from "lucide-react";
import { useTranslations } from "next-intl";

export default function CtaGuideHero() {
  const t = useTranslations("seeker");
  const scrollToEmailForm = () => {
    const emailSection = document.getElementById('email-capture-section');
    emailSection?.scrollIntoView({ behavior: 'smooth' });

    // Track CTA click
    if (typeof window !== 'undefined' && window.gtag) {
      gtagEvent({
        label: "Hero CTA",
        category: "Lead Magnet",
        action: "guide_download_cta_click",
        value: "1"
      })
    }
  };
  return <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
    <Button
      onClick={scrollToEmailForm}
      size="lg"
      className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
    >
      <Download className="mr-2 h-5 w-5" />
      {t('guide.hero.cta.primaryrimary')}
    </Button>

    <Button
      variant="outline"
      onClick={scrollToEmailForm}
      size="lg"
      className="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 text-lg font-semibold rounded-lg transition-all duration-300 bg-transparent"
    >
      <ArrowDown className="mr-2 h-5 w-5" />
      {t('guide.hero.cta.secondaryondary')}
    </Button>
  </div>
}