"use client"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import React, { ComponentProps } from "react"

interface ListingsLayoutProps extends Omit<ComponentProps<"div">, "title"> {
  title: React.ReactNode,
  description?: string
  action?: {
    title: string,
    action: () => void
  }
}
export default function DefaultLayoutContent({ title, description, action, ...rest }: ListingsLayoutProps) {
  return <section {...rest} className={cn("space-y-6", rest.className)} >
    <div className="flex justify-between gap-2">
      <div className="space-y-1">
        <h2 className="capitalize text-seekers-text text-2xl font-bold tracking-[0.5%]">{title}</h2>
        <p className=" text-seekers-text-light text-base font-semibold tracking-[0.5%]">{description}</p>
      </div>
      {action && <Button variant={"link"} className="text-seekers-primary-foreground" onClick={action.action}>
        {action.title}
      </Button>}
    </div>
    {rest.children}
  </section>
}