import { useSeekerSearchStore } from "@/stores/seeker-search.store";
import { BaseSelectInputValue } from "@/types/base";
import { useMemo, useState } from "react";
import { searchUrl } from "@/lib/constanta/route";
import { slugGenerator } from "@/lib/utils";
import { useRouter } from "nextjs-toploader/app";
import { useTranslations } from "next-intl";
import { listingCategory } from "@/core/domain/listing/listing-seekers";
import { filterTitles } from "@/lib/constanta/constant";

export default function useSeekersSearch() {
  const t = useTranslations();
  const seekersSearch = useSeekerSearchStore((state) => state);
  const [showBanjars, setShowBanjars] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<string | null>(null);
  const router = useRouter();
  const propertyType: BaseSelectInputValue<string>[] = [
    {
      content: t("seeker.listing.category.villa"),
      id: "1",
      value: listingCategory.villas,
    },
    {
      content: t("seeker.listing.category.apartment"),
      id: "2",
      value: listingCategory.apartment,
    },
    {
      content: t("seeker.listing.category.guestHouse"),
      id: "3",
      value: listingCategory.rooms,
    },
    {
      content: t("seeker.listing.category.commercial"),
      id: "4",
      value: listingCategory.commercialSpace,
    },
    {
      content: t("seeker.listing.category.cafeAndRestaurent"),
      id: "5",
      value: listingCategory.cafeOrRestaurants,
    },
    {
      content: t("seeker.listing.category.office"),
      id: "6",
      value: listingCategory.offices,
    },
    {
      content: t("seeker.listing.category.shops"),
      id: "7",
      value: listingCategory.shops,
    },
    {
      content: t("seeker.listing.category.shellAndCore"),
      id: "8",
      value: listingCategory.shellAndCore,
    },
    {
      content: t("seeker.listing.category.land"),
      id: "9",
      value: listingCategory.lands,
    },
  ];
  const locations = [
    {
      name: "Canggu, Bali",
      description: "Popular surf spot & digital nomad hub",
      icon: "Canggu",
      value: "canggu",
    },
    {
      name: "Ubud, Bali",
      description: "Cultural heart with rice terraces",
      value: "ubud",
      icon: "Ubud",
    },
    {
      name: "Seminyak, Bali",
      description: "Upscale beach resort area",
      icon: "Seminyak",
      value: "seminyak",
    },
    {
      name: "Uluwatu, Bali",
      description: "Clifftop temples & luxury resorts",
      icon: "Uluwatu",
      value: "uluwatu",
    },
    {
      name: "Nusa Dua, Bali",
      description: "Gated resort area with pristine beaches",
      icon: "NusaDua",
      value: "Nusa Dua",
    },
  ] as const;

  const banjars = {
    canggu: [
      "Babakan",
      "Batu Bolong",
      "Berawa",
      "Cemagi",
      "Cempaka",
      "Echo Beach",
      "Kayu Tulang",
      "Munggu",
      "Nelayan",
      "North Canggu",
      "Nyanyi",
      "Padonan",
      "Pantai Lima",
      "Pererenan",
      "Seseh",
      "Tiying Tutul",
      "Tumbak Bayuh",
    ].sort(),
    ubud: [
      "Bentuyung",
      "Junjungan",
      "Kedewatan",
      "Nyuh Kuning",
      "Penestanan",
      "Sambahan",
      "Sanggingan",
      "Taman Kaja",
      "Tegallantang",
      "Ubud Center",
    ],
    uluwatu: [
      "Balangan",
      "Bingin",
      "Green Bowl",
      "Karang Boma",
      "Nyang Nyang",
      "Padang Padang",
      "Pecatu",
      "Suluban",
    ],
    nusaDua: [
      "Benoa",
      "BTDC Area",
      "Bualu",
      "Kampial",
      "Peminge",
      "Sawangan",
      "Tanjung Benoa",
    ],
    seminyak: [],
  } as const;

  const filteredLocations = useMemo(() => {
    if (!seekersSearch.query || showBanjars) return locations;

    return locations.filter((location) => {
      const cleanLocationName = location.name
        .replace(", Bali", "")
        .toLowerCase();
      const searchTermLower = seekersSearch.query.toLowerCase();

      // Check if location name matches
      if (cleanLocationName.includes(searchTermLower)) return true;

      // Check if any banjar in this location matches
      const locationBanjars =
        banjars[location.value as keyof typeof banjars] || [];
      return locationBanjars.some((banjar) =>
        banjar.toLowerCase().includes(searchTermLower)
      );
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [seekersSearch.query, showBanjars]);

  const getMatchingBanjars = (locationName: string) => {
    const query = seekersSearch.query;
    if (!query) return [];

    const locationBanjars = banjars[locationName as keyof typeof banjars] || [];
    return locationBanjars.filter((banjar) =>
      banjar.toLowerCase().includes(query.toLowerCase())
    );
  };
  const handleSetQuery = (val: string) => {
    const data = val.split(",");
    const stringLength = val.length;
    if (data.length > 3 && val.charAt(stringLength - 1) == ",") return;
    seekersSearch.setQuery(val);
  };
  const handleSetType = (val: string) => {
    if (
      seekersSearch.propertyType.length >= 3 &&
      !seekersSearch.propertyType.includes(val)
    )
      return;
    seekersSearch.setPropertyType(val);
  };
  const handleSearch = (query?: string, propertyType?: string[]) => {
    if (query) {
      seekersSearch.setQuery(query);
    }
    if (propertyType) {
      seekersSearch.setPropertyTypeFromArray(propertyType);
    }
    const querySearch = query || seekersSearch.query;
    const propertyTypeSearch = propertyType || seekersSearch.propertyType;
    if (seekersSearch.activeSearch.query !== "") {
      seekersSearch.setSearchHistory({
        propertyType: seekersSearch.activeSearch.propertyType,
        query: seekersSearch.activeSearch.query,
      });
    }
    seekersSearch.setActiveSearch({
      query: querySearch,
      propertyType: propertyTypeSearch,
    });
    const slug = slugGenerator(querySearch);
    router.push(
      searchUrl +
        "/" +
        (slug || "all") +
        "?" +
        filterTitles.type +
        "=" +
        (propertyTypeSearch.toString() || "all")
    );
  };

  const propertyTypeFormatHelper = (values: string[]) => {
    const data = values.map((item) => {
      const property = propertyType.find((type) => type.value == item);
      return property?.content;
    });
    return data;
  };
  const handleSelectLocation = (val: string) => {
    setShowBanjars(true);
    setSelectedLocation(val);
    seekersSearch.setQuery(val);
  };
  const handleBackToLocations = () => {
    setShowBanjars(false);
    const query = seekersSearch.query;
    const finalQuery = query.replace(selectedLocation || "", "");
    seekersSearch.setQuery(finalQuery);
    // setSelectedBanjars([])
  };
  const handleSetBanjar = (val: string, isManualSearch = false) => {
    const maximumQueryLength = 3;
    const query = seekersSearch.query
      .split(",")
      .filter((item) => item.trim() !== "" && item !== selectedLocation);

    // removing search query based on {val}
    if (query.includes(val)) {
      const finalQuery = query.filter((item) => item !== val);
      seekersSearch.setQuery(finalQuery.toString());
      return;
    }

    // do nothing if query exeeding maximum query length
    if (query.length >= maximumQueryLength && query[query.length - 1] !== "")
      return;

    // this happen when user trying search by typing the location name
    if (isManualSearch) {
      const querylength = query.length;
      query[querylength - 1] = val;
    } else {
      query.push(val);
    }

    seekersSearch.setQuery(query.toString());
    return;
  };
  return {
    seekersSearch,
    handleSetQuery,
    handleSetType,
    propertyType,
    handleSearch,
    propertyTypeFormatHelper,
    locations,
    banjars,
    getMatchingBanjars,
    showBanjars,
    setShowBanjars,
    selectedLocation,
    setSelectedLocation,
    handleSelectLocation,
    handleBackToLocations,
    handleSetBanjar,
    filteredLocations,
  };
}
