import { useSeekerFilterStore } from "@/stores/seekers-filter-2.store";
import { useTranslations } from "next-intl";
import useSearchParamWrapper from "./use-search-param-wrapper";
import { useSeekerSearchStore } from "@/stores/seeker-search.store";
import { values } from "lodash";
import { filterTitles } from "@/lib/constanta/constant";
import { useGetFilterParameter } from "@/core/applications/queries/listing/use-get-filter-parameters";
import {
  BusinessBuildingSize,
  businessBuildingSize,
  listingCategory,
  seekersListingFilterType,
} from "@/core/domain/listing/listing-seekers";
import { useSettingStore } from "@/stores/setting.store";
import { useSeekersSettingsStore } from "@/stores/seekers-settings.store";

interface Params {
  name: string;
  value: string;
}
export interface SearchParams {
  query: string;
  types: string;
  minPrice?: number;
  maxPrice?: number;
  LandLargest?: number;
  LandSmallest?: number;
  buildingLargest?: number;
  buildingSmallest?: number;
  gardenLargest?: number;
  gardenSmallest?: number;
  yearsOfBuilding?: string;
  bedroomTotal?: number;
  bathroomTotal?: number;
  rentalOffers?: string;
  feature?: string;
  sellingPoints?: string;
  page: string;
  lat?: string; //North west
  lng?: string; // South East,
  sortBy?: string;
  electricity?: string;
  parkingOption?: string;
  poolOption?: string;
  furnishingOption?: string;
  typeLiving?: string;
  propertyOfView?: string;
  propertyLocation?: string;
  minimumContract?: string;
  type_category?: string;
  zoom?: string;
  perPage?: string;
}

// since we convert the currency on UI, we need to use currencyRate in order to reverse the currency to IDR
export default function useSeekersFilter(conversions: {
  [key: string]: number;
}) {
  const { currency } = useSeekersSettingsStore();
  const seekersFilter = useSeekerFilterStore();
  const seekersSearch = useSeekerSearchStore((state) => state);
  const { createMultipleQueryString } = useSearchParamWrapper();
  const filterParameterQuery = useGetFilterParameter();

  const handleFilter = () => {
    const conversionRate = conversions[currency];
    if (filterParameterQuery.isPending) return;
    const bedroom =
      seekersFilter.bedRoom == "any" ? "0" : seekersFilter.bedRoom;
    const bathRoom =
      seekersFilter.bathRoom == "any" ? "0" : seekersFilter.bathRoom;
    let minBuildingSize = seekersFilter.buildingSize.min;
    let maxBuildingSize = seekersFilter.buildingSize.max;
    let typeProperty = [];

    if (seekersFilter.typeProperty == seekersListingFilterType.business) {
      typeProperty = [
        listingCategory.commercialSpace,
        listingCategory.cafeOrRestaurants,
        listingCategory.shops,
        listingCategory.offices,
      ];
      if (seekersFilter.subTypeProperty.length == 3) {
        minBuildingSize = businessBuildingSize.small.min;
        maxBuildingSize = businessBuildingSize.large.max;
      } else if (seekersFilter.subTypeProperty.length == 2) {
        if (
          seekersFilter.subTypeProperty.includes(businessBuildingSize.small.key)
        ) {
          minBuildingSize = businessBuildingSize.small.min;
          maxBuildingSize = businessBuildingSize.medium.max;
        } else {
          minBuildingSize = businessBuildingSize.medium.min;
          maxBuildingSize = businessBuildingSize.large.max;
        }
      } else {
        minBuildingSize =
          businessBuildingSize[
            seekersFilter.subTypeProperty[0] as BusinessBuildingSize
          ].min;
        maxBuildingSize =
          businessBuildingSize[
            seekersFilter.subTypeProperty[0] as BusinessBuildingSize
          ].max;
      }
    } else if (
      seekersFilter.typeProperty == seekersListingFilterType.placeToLive
    ) {
      typeProperty = seekersFilter.subTypeProperty;
    } else if (seekersFilter.typeProperty == seekersListingFilterType.land) {
      typeProperty = [listingCategory.lands];
    } else {
      typeProperty = seekersSearch.propertyType;
    }

    const type: Params = {
      name: filterTitles.type,
      value: typeProperty.toString(),
    };
    const minPrice: Params = {
      name: filterTitles.minPrice,
      value: (seekersFilter.priceRange.min / conversionRate)
        .toFixed(0)
        .toString(),
    };
    const maxPrice: Params = {
      name: filterTitles.maxPrice,
      value: (seekersFilter.priceRange.max / conversionRate)
        .toFixed(0)
        .toString(),
    };
    const LandLargest: Params = {
      name: filterTitles.landLargest,
      value: seekersFilter.landSize.max.toString(),
    };
    const LandSmallest: Params = {
      name: filterTitles.landSmallest,
      value: seekersFilter.landSize.min.toString(),
    };
    const buildingLargest: Params = {
      name: filterTitles.buildingLargest,
      value: maxBuildingSize.toString(),
    };
    const buildingSmallest: Params = {
      name: filterTitles.buildingSmallest,
      value: minBuildingSize.toString(),
    };
    const gardenLargest: Params = {
      name: filterTitles.gardenLargest,
      value: seekersFilter.gardenSize.max.toString(),
    };
    const gardenSmallest: Params = {
      name: filterTitles.gardenSmallest,
      value: seekersFilter.gardenSize.min.toString(),
    };
    const yearsOfBuilding: Params = {
      name: filterTitles.yearsOfBuild,
      value:
        seekersFilter.yearsOfBuild == "ANY" ? "" : seekersFilter.yearsOfBuild,
    };
    const bedrooms: Params = {
      name: filterTitles.bedroomTotal,
      value: bedroom,
    };
    const bathroom: Params = {
      name: filterTitles.bathroomTotal,
      value: bathRoom,
    };
    const rentalOffer: Params = {
      name: filterTitles.rentalOffer,
      value: seekersFilter.rentalIncluding.toString(),
    };
    const propertyCondition: Params = {
      name: filterTitles.propertyCondition,
      value: seekersFilter.propertyCondition.toString(),
    };
    const electricity: Params = {
      name: filterTitles.electircity,
      value: seekersFilter.electricity,
    };
    const view: Params = {
      name: filterTitles.view,
      value: seekersFilter.view.toString(),
    };
    const parking: Params = {
      name: filterTitles.parking,
      value:
        seekersFilter.parkingStatus == "ANY" ? "" : seekersFilter.parkingStatus,
    };
    const swimmingPool: Params = {
      name: filterTitles.swimmingPool,
      value: seekersFilter.poolStatus == "ANY" ? "" : seekersFilter.poolStatus,
    };
    const typeLiving: Params = {
      name: filterTitles.typeLiving,
      value: seekersFilter.typeLiving == "ANY" ? "" : seekersFilter.typeLiving,
    };
    const furnished: Params = {
      name: filterTitles.furnished,
      value:
        seekersFilter.furnishedStatus == "ANY"
          ? ""
          : seekersFilter.furnishedStatus,
    };
    const minimumContract: Params = {
      name: filterTitles.minimumContract,
      value:
        seekersFilter.minimumContract == "ANY"
          ? ""
          : seekersFilter.minimumContract,
    };
    const category: Params = {
      name: filterTitles.category,
      value: seekersFilter.typeProperty,
    };
    const subCategory: Params = {
      name: filterTitles.subCategory,
      value: seekersFilter.subTypeProperty.toString(),
    };
    const feature: Params = {
      name: filterTitles.feature,
      value: seekersFilter.features.toString(),
    };
    const propertyLocation: Params = {
      name: filterTitles.propertyLocation,
      value: seekersFilter.location.toString(),
    };

    createMultipleQueryString([
      minPrice,
      maxPrice,
      yearsOfBuilding,
      bedrooms,
      bathroom,
      type,
      rentalOffer,
      propertyCondition,
      electricity,
      view,
      parking,
      swimmingPool,
      typeLiving,
      furnished,
      minimumContract,
      category,
      subCategory,
      feature,
      LandLargest,
      LandSmallest,
      buildingLargest,
      buildingSmallest,
      gardenLargest,
      gardenSmallest,
      propertyLocation,
    ]);
  };
  const handleClearFilter = () => {
    const priceRange = filterParameterQuery.data?.data?.priceRange;
    const landRange = filterParameterQuery.data?.data?.landSizeRange;
    const buildingRange = filterParameterQuery.data?.data?.buildingSizeRange;
    const gardenRange = filterParameterQuery.data?.data?.gardenSizeRange;
    seekersFilter.resetFilters();
    if (priceRange) {
      seekersFilter.setPriceRange(priceRange.min, priceRange.max);
    }
    if (landRange) {
      seekersFilter.setLandSize(landRange.min, landRange.max);
    }
    if (buildingRange) {
      seekersFilter.setBuildingSize(buildingRange.min, buildingRange.max);
    }
    if (gardenRange) {
      seekersFilter.setGardenSize(gardenRange.min, gardenRange.max);
    }
  };
  return { handleFilter, handleClearFilter };
}
